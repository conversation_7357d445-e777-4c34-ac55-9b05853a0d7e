/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./node_modules/@assistant-ui/react-markdown/styles/dot.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
@keyframes aui-pulse {
  50% {
    opacity: 0.5;
  }
}

:where(.aui-md[data-status="running"]):empty::after,
:where(.aui-md[data-status="running"])
  > :where(:not(ol):not(ul):not(pre)):last-child::after,
:where(.aui-md[data-status="running"]) > pre:last-child code::after,
:where(.aui-md[data-status="running"])
  > :where(:is(ol, ul):last-child)
  > :where(li:last-child:not(:has(* > li)))::after,
:where(.aui-md[data-status="running"])
  > :where(:is(ol, ul):last-child)
  > :where(li:last-child)
  > :where(:is(ol, ul):last-child)
  > :where(li:last-child:not(:has(* > li)))::after,
:where(.aui-md[data-status="running"])
  > :where(:is(ol, ul):last-child)
  > :where(li:last-child)
  > :where(:is(ol, ul):last-child)
  > :where(li:last-child)
  > :where(:is(ol, ul):last-child)
  > :where(li:last-child)::after {
  animation: aui-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --aui-content: "\25cf";
  content: var(--aui-content);
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

