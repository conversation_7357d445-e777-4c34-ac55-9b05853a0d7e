{"sortedMiddleware": [], "middleware": {}, "functions": {"/api/chat/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/chat/route.js"], "name": "app/api/chat/route", "page": "/api/chat/route", "matchers": [{"regexp": "^/api/chat$", "originalSource": "/api/chat"}], "wasm": [], "assets": []}, "/api/extract-keywords/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/extract-keywords/route.js"], "name": "app/api/extract-keywords/route", "page": "/api/extract-keywords/route", "matchers": [{"regexp": "^/api/extract\\-keywords$", "originalSource": "/api/extract-keywords"}], "wasm": [], "assets": []}, "/api/images/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/images/route.js"], "name": "app/api/images/route", "page": "/api/images/route", "matchers": [{"regexp": "^/api/images$", "originalSource": "/api/images"}], "wasm": [], "assets": []}}, "version": 2}