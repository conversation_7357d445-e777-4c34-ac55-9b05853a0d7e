"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n    return function handleEvent(event) {\n        originalEventHandler?.(event);\n        if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n            return ourEventHandler?.(event);\n        }\n    };\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNENBQTRDO0FBQzVDLFNBQVNBLHFCQUFxQkMsb0JBQW9CLEVBQUVDLGVBQWUsRUFBRSxFQUFFQywyQkFBMkIsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO0lBQzNHLE9BQU8sU0FBU0MsWUFBWUMsS0FBSztRQUMvQkosdUJBQXVCSTtRQUN2QixJQUFJRiw2QkFBNkIsU0FBUyxDQUFDRSxNQUFNQyxnQkFBZ0IsRUFBRTtZQUNqRSxPQUFPSixrQkFBa0JHO1FBQzNCO0lBQ0Y7QUFDRjtBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzPzE4NjgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9wcmltaXRpdmUvc3JjL3ByaW1pdGl2ZS50c3hcbmZ1bmN0aW9uIGNvbXBvc2VFdmVudEhhbmRsZXJzKG9yaWdpbmFsRXZlbnRIYW5kbGVyLCBvdXJFdmVudEhhbmRsZXIsIHsgY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID0gdHJ1ZSB9ID0ge30pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIGhhbmRsZUV2ZW50KGV2ZW50KSB7XG4gICAgb3JpZ2luYWxFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgaWYgKGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9PT0gZmFsc2UgfHwgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgIHJldHVybiBvdXJFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgfVxuICB9O1xufVxuZXhwb3J0IHtcbiAgY29tcG9zZUV2ZW50SGFuZGxlcnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiY29tcG9zZUV2ZW50SGFuZGxlcnMiLCJvcmlnaW5hbEV2ZW50SGFuZGxlciIsIm91ckV2ZW50SGFuZGxlciIsImNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCIsImhhbmRsZUV2ZW50IiwiZXZlbnQiLCJkZWZhdWx0UHJldmVudGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-arrow/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/arrow.tsx\n\n\n\nvar NAME = \"Arrow\";\nvar Arrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { children, width = 10, height = 5, ...arrowProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.svg, {\n        ...arrowProps,\n        ref: forwardedRef,\n        width,\n        height,\n        viewBox: \"0 0 30 10\",\n        preserveAspectRatio: \"none\",\n        children: props.asChild ? children : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", {\n            points: \"0,0 30,0 15,10\"\n        })\n    });\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        return ref(value);\n    } else if (ref !== null && ref !== void 0) {\n        ref.current = value;\n    }\n}\nfunction composeRefs(...refs) {\n    return (node)=>{\n        let hasCleanup = false;\n        const cleanups = refs.map((ref)=>{\n            const cleanup = setRef(ref, node);\n            if (!hasCleanup && typeof cleanup == \"function\") {\n                hasCleanup = true;\n            }\n            return cleanup;\n        });\n        if (hasCleanup) {\n            return ()=>{\n                for(let i = 0; i < cleanups.length; i++){\n                    const cleanup = cleanups[i];\n                    if (typeof cleanup == \"function\") {\n                        cleanup();\n                    } else {\n                        setRef(refs[i], null);\n                    }\n                }\n            };\n        }\n    };\n}\nfunction useComposedRefs(...refs) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n    const Context = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const Provider = (props)=>{\n        const { children, ...context } = props;\n        const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n            value,\n            children\n        });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName) {\n        const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n        if (context) return context;\n        if (defaultContext !== void 0) return defaultContext;\n        throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [\n        Provider,\n        useContext2\n    ];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n    let defaultContexts = [];\n    function createContext3(rootComponentName, defaultContext) {\n        const BaseContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        const index = defaultContexts.length;\n        defaultContexts = [\n            ...defaultContexts,\n            defaultContext\n        ];\n        const Provider = (props)=>{\n            const { scope, children, ...context } = props;\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n                value,\n                children\n            });\n        };\n        Provider.displayName = rootComponentName + \"Provider\";\n        function useContext2(consumerName, scope) {\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n            if (context) return context;\n            if (defaultContext !== void 0) return defaultContext;\n            throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n        }\n        return [\n            Provider,\n            useContext2\n        ];\n    }\n    const createScope = ()=>{\n        const scopeContexts = defaultContexts.map((defaultContext)=>{\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        });\n        return function useScope(scope) {\n            const contexts = scope?.[scopeName] || scopeContexts;\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${scopeName}`]: {\n                        ...scope,\n                        [scopeName]: contexts\n                    }\n                }), [\n                scope,\n                contexts\n            ]);\n        };\n    };\n    createScope.scopeName = scopeName;\n    return [\n        createContext3,\n        composeContextScopes(createScope, ...createContextScopeDeps)\n    ];\n}\nfunction composeContextScopes(...scopes) {\n    const baseScope = scopes[0];\n    if (scopes.length === 1) return baseScope;\n    const createScope = ()=>{\n        const scopeHooks = scopes.map((createScope2)=>({\n                useScope: createScope2(),\n                scopeName: createScope2.scopeName\n            }));\n        return function useComposedScopes(overrideScopes) {\n            const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName })=>{\n                const scopeProps = useScope(overrideScopes);\n                const currentScope = scopeProps[`__scope${scopeName}`];\n                return {\n                    ...nextScopes2,\n                    ...currentScope\n                };\n            }, {});\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${baseScope.scopeName}`]: nextScopes\n                }), [\n                nextScopes\n            ]);\n        };\n    };\n    createScope.scopeName = baseScope.scopeName;\n    return createScope;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useId \".trim().toString()] || (()=>void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n    const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if (!deterministicId) setId((reactId)=>reactId ?? String(count++));\n    }, [\n        deterministicId\n    ]);\n    return deterministicId || (id ? `radix-${id}` : \"\");\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSwrQkFBK0I7QUFDQTtBQUNxQztBQUNwRSxJQUFJRSxhQUFhRix5TEFBSyxDQUFDLFVBQVVHLElBQUksR0FBR0MsUUFBUSxHQUFHLElBQUssS0FBTSxLQUFLO0FBQ25FLElBQUlDLFFBQVE7QUFDWixTQUFTQyxNQUFNQyxlQUFlO0lBQzVCLE1BQU0sQ0FBQ0MsSUFBSUMsTUFBTSxHQUFHVCwyQ0FBYyxDQUFDRTtJQUNuQ0Qsa0ZBQWVBLENBQUM7UUFDZCxJQUFJLENBQUNNLGlCQUFpQkUsTUFBTSxDQUFDRSxVQUFZQSxXQUFXQyxPQUFPUDtJQUM3RCxHQUFHO1FBQUNFO0tBQWdCO0lBQ3BCLE9BQU9BLG1CQUFvQkMsQ0FBQUEsS0FBSyxDQUFDLE1BQU0sRUFBRUEsR0FBRyxDQUFDLEdBQUcsRUFBQztBQUNuRDtBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtaWQvZGlzdC9pbmRleC5tanM/YzY0OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9pZC9zcmMvaWQudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3RcIjtcbnZhciB1c2VSZWFjdElkID0gUmVhY3RbXCIgdXNlSWQgXCIudHJpbSgpLnRvU3RyaW5nKCldIHx8ICgoKSA9PiB2b2lkIDApO1xudmFyIGNvdW50ID0gMDtcbmZ1bmN0aW9uIHVzZUlkKGRldGVybWluaXN0aWNJZCkge1xuICBjb25zdCBbaWQsIHNldElkXSA9IFJlYWN0LnVzZVN0YXRlKHVzZVJlYWN0SWQoKSk7XG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFkZXRlcm1pbmlzdGljSWQpIHNldElkKChyZWFjdElkKSA9PiByZWFjdElkID8/IFN0cmluZyhjb3VudCsrKSk7XG4gIH0sIFtkZXRlcm1pbmlzdGljSWRdKTtcbiAgcmV0dXJuIGRldGVybWluaXN0aWNJZCB8fCAoaWQgPyBgcmFkaXgtJHtpZH1gIDogXCJcIik7XG59XG5leHBvcnQge1xuICB1c2VJZFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUxheW91dEVmZmVjdCIsInVzZVJlYWN0SWQiLCJ0cmltIiwidG9TdHJpbmciLCJjb3VudCIsInVzZUlkIiwiZGV0ZXJtaW5pc3RpY0lkIiwiaWQiLCJzZXRJZCIsInVzZVN0YXRlIiwicmVhY3RJZCIsIlN0cmluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // src/popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: (...args)=>{\n            const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                animationFrame: updatePositionStrategy === \"always\"\n            });\n            return cleanup;\n        },\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: ({ elements, rects, availableWidth, availableHeight })=>{\n                    const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                    const contentStyle = elements.floating.style;\n                    contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                }\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (isPositioned) {\n            handlePlaced?.();\n        }\n    }, [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            const ownerWindow = node.ownerDocument.defaultView ?? window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            stylesRef.current = node2 ? getComputedStyle(node2) : null;\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n    \"a\",\n    \"button\",\n    \"div\",\n    \"form\",\n    \"h2\",\n    \"h3\",\n    \"img\",\n    \"input\",\n    \"label\",\n    \"li\",\n    \"nav\",\n    \"ol\",\n    \"p\",\n    \"select\",\n    \"span\",\n    \"svg\",\n    \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node)=>{\n    const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n    const Node = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { asChild, ...primitiveProps } = props;\n        const Comp = asChild ? Slot : node;\n        if (false) {}\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, {\n            ...primitiveProps,\n            ref: forwardedRef\n        });\n    });\n    Node.displayName = `Primitive.${node}`;\n    return {\n        ...primitive,\n        [node]: Node\n    };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n    if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>target.dispatchEvent(event));\n}\nvar Root = Primitive;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Root,Slot,Slottable,createSlot,createSlottable auto */ // src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n    const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n    const Slot2 = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n        const slottable = childrenArray.find(isSlottable);\n        if (slottable) {\n            const newElement = slottable.props.children;\n            const newChildren = childrenArray.map((child)=>{\n                if (child === slottable) {\n                    if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n                } else {\n                    return child;\n                }\n            });\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n                ...slotProps,\n                ref: forwardedRef,\n                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null\n            });\n        }\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n            ...slotProps,\n            ref: forwardedRef,\n            children\n        });\n    });\n    Slot2.displayName = `${ownerName}.Slot`;\n    return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n    const SlotClone = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        const childrenRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children) ? getElementRef(children) : void 0;\n        const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(childrenRef, forwardedRef);\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n            const props2 = mergeProps(slotProps, children.props);\n            if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n                props2.ref = ref;\n            }\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n        }\n        return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n    });\n    SlotClone.displayName = `${ownerName}.SlotClone`;\n    return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n    const Slottable2 = ({ children })=>{\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n            children\n        });\n    };\n    Slottable2.displayName = `${ownerName}.Slottable`;\n    Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n    return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n    const overrideProps = {\n        ...childProps\n    };\n    for(const propName in childProps){\n        const slotPropValue = slotProps[propName];\n        const childPropValue = childProps[propName];\n        const isHandler = /^on[A-Z]/.test(propName);\n        if (isHandler) {\n            if (slotPropValue && childPropValue) {\n                overrideProps[propName] = (...args)=>{\n                    const result = childPropValue(...args);\n                    slotPropValue(...args);\n                    return result;\n                };\n            } else if (slotPropValue) {\n                overrideProps[propName] = slotPropValue;\n            }\n        } else if (propName === \"style\") {\n            overrideProps[propName] = {\n                ...slotPropValue,\n                ...childPropValue\n            };\n        } else if (propName === \"className\") {\n            overrideProps[propName] = [\n                slotPropValue,\n                childPropValue\n            ].filter(Boolean).join(\" \");\n        }\n    }\n    return {\n        ...slotProps,\n        ...overrideProps\n    };\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-tooltip/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipArrow: () => (/* binding */ TooltipArrow),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipPortal: () => (/* binding */ TooltipPortal),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTooltipScope: () => (/* binding */ createTooltipScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Portal,Provider,Root,Tooltip,TooltipArrow,TooltipContent,TooltipPortal,TooltipProvider,TooltipTrigger,Trigger,createTooltipScope auto */ // src/tooltip.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar [createTooltipContext, createTooltipScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(\"Tooltip\", [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props)=>{\n    const { __scopeTooltip, delayDuration = DEFAULT_DELAY_DURATION, skipDelayDuration = 300, disableHoverableContent = false, children } = props;\n    const isOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const isPointerInTransitRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const skipDelayTimer = skipDelayTimerRef.current;\n        return ()=>window.clearTimeout(skipDelayTimer);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipProviderContextProvider, {\n        scope: __scopeTooltip,\n        isOpenDelayedRef,\n        delayDuration,\n        onOpen: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            window.clearTimeout(skipDelayTimerRef.current);\n            isOpenDelayedRef.current = false;\n        }, []),\n        onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            window.clearTimeout(skipDelayTimerRef.current);\n            skipDelayTimerRef.current = window.setTimeout(()=>isOpenDelayedRef.current = true, skipDelayDuration);\n        }, [\n            skipDelayDuration\n        ]),\n        isPointerInTransitRef,\n        onPointerInTransitChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((inTransit)=>{\n            isPointerInTransitRef.current = inTransit;\n        }, []),\n        disableHoverableContent,\n        children\n    });\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props)=>{\n    const { __scopeTooltip, children, open: openProp, defaultOpen, onOpenChange, disableHoverableContent: disableHoverableContentProp, delayDuration: delayDurationProp } = props;\n    const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n    const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n    const wasOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: (open2)=>{\n            if (open2) {\n                providerContext.onOpen();\n                document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n            } else {\n                providerContext.onClose();\n            }\n            onOpenChange?.(open2);\n        },\n        caller: TOOLTIP_NAME\n    });\n    const stateAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n    }, [\n        open\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n        wasOpenDelayedRef.current = false;\n        setOpen(true);\n    }, [\n        setOpen\n    ]);\n    const handleClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n        setOpen(false);\n    }, [\n        setOpen\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = window.setTimeout(()=>{\n            wasOpenDelayedRef.current = true;\n            setOpen(true);\n            openTimerRef.current = 0;\n        }, delayDuration);\n    }, [\n        delayDuration,\n        setOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (openTimerRef.current) {\n                window.clearTimeout(openTimerRef.current);\n                openTimerRef.current = 0;\n            }\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContextProvider, {\n            scope: __scopeTooltip,\n            contentId,\n            open,\n            stateAttribute,\n            trigger,\n            onTriggerChange: setTrigger,\n            onTriggerEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n                if (providerContext.isOpenDelayedRef.current) handleDelayedOpen();\n                else handleOpen();\n            }, [\n                providerContext.isOpenDelayedRef,\n                handleDelayedOpen,\n                handleOpen\n            ]),\n            onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n                if (disableHoverableContent) {\n                    handleClose();\n                } else {\n                    window.clearTimeout(openTimerRef.current);\n                    openTimerRef.current = 0;\n                }\n            }, [\n                handleClose,\n                disableHoverableContent\n            ]),\n            onOpen: handleOpen,\n            onClose: handleClose,\n            disableHoverableContent,\n            children\n        })\n    });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handlePointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>isPointerDownRef.current = false, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>document.removeEventListener(\"pointerup\", handlePointerUp);\n    }, [\n        handlePointerUp\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            \"aria-describedby\": context.open ? context.contentId : void 0,\n            \"data-state\": context.stateAttribute,\n            ...triggerProps,\n            ref: composedRefs,\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                if (event.pointerType === \"touch\") return;\n                if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n                    context.onTriggerEnter();\n                    hasPointerMoveOpenedRef.current = true;\n                }\n            }),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerLeave, ()=>{\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, ()=>{\n                if (context.open) {\n                    context.onClose();\n                }\n                isPointerDownRef.current = true;\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    once: true\n                });\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                if (!isPointerDownRef.current) context.onOpen();\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onBlur, context.onClose),\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onClose)\n        })\n    });\n});\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar TooltipPortal = (props)=>{\n    const { __scopeTooltip, forceMount, children, container } = props;\n    const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeTooltip,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.disableHoverableContent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentHoverable, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar TooltipContentHoverable = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref);\n    const [pointerGraceArea, setPointerGraceArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const { trigger, onClose } = context;\n    const content = ref.current;\n    const { onPointerInTransitChange } = providerContext;\n    const handleRemoveGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        setPointerGraceArea(null);\n        onPointerInTransitChange(false);\n    }, [\n        onPointerInTransitChange\n    ]);\n    const handleCreateGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event, hoverTarget)=>{\n        const currentTarget = event.currentTarget;\n        const exitPoint = {\n            x: event.clientX,\n            y: event.clientY\n        };\n        const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n        const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n        const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n        const graceArea = getHull([\n            ...paddedExitPoints,\n            ...hoverTargetPoints\n        ]);\n        setPointerGraceArea(graceArea);\n        onPointerInTransitChange(true);\n    }, [\n        onPointerInTransitChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>handleRemoveGraceArea();\n    }, [\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trigger && content) {\n            const handleTriggerLeave = (event)=>handleCreateGraceArea(event, content);\n            const handleContentLeave = (event)=>handleCreateGraceArea(event, trigger);\n            trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n            content.addEventListener(\"pointerleave\", handleContentLeave);\n            return ()=>{\n                trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n                content.removeEventListener(\"pointerleave\", handleContentLeave);\n            };\n        }\n    }, [\n        trigger,\n        content,\n        handleCreateGraceArea,\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (pointerGraceArea) {\n            const handleTrackPointerGrace = (event)=>{\n                const target = event.target;\n                const pointerPosition = {\n                    x: event.clientX,\n                    y: event.clientY\n                };\n                const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n                const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n                if (hasEnteredTarget) {\n                    handleRemoveGraceArea();\n                } else if (isPointerOutsideGraceArea) {\n                    handleRemoveGraceArea();\n                    onClose();\n                }\n            };\n            document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n            return ()=>document.removeEventListener(\"pointermove\", handleTrackPointerGrace);\n        }\n    }, [\n        trigger,\n        content,\n        pointerGraceArea,\n        onClose,\n        handleRemoveGraceArea\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n        ...props,\n        ref: composedRefs\n    });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, {\n    isInside: false\n});\nvar Slottable = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.createSlottable)(\"TooltipContent\");\nvar TooltipContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, children, \"aria-label\": ariaLabel, onEscapeKeyDown, onPointerDownOutside, ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        document.addEventListener(TOOLTIP_OPEN, onClose);\n        return ()=>document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [\n        onClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (context.trigger) {\n            const handleScroll = (event)=>{\n                const target = event.target;\n                if (target?.contains(context.trigger)) onClose();\n            };\n            window.addEventListener(\"scroll\", handleScroll, {\n                capture: true\n            });\n            return ()=>window.removeEventListener(\"scroll\", handleScroll, {\n                    capture: true\n                });\n        }\n    }, [\n        context.trigger,\n        onClose\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__.DismissableLayer, {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event)=>event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n                ...contentProps.style,\n                // re-namespace exposed content custom properties\n                ...{\n                    \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                }\n            },\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Slottable, {\n                    children\n                }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VisuallyHiddenContentContextProvider, {\n                    scope: __scopeTooltip,\n                    isInside: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        id: context.contentId,\n                        role: \"tooltip\",\n                        children: ariaLabel || children\n                    })\n                })\n            ]\n        })\n    });\n});\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(ARROW_NAME, __scopeTooltip);\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n    const top = Math.abs(rect.top - point.y);\n    const bottom = Math.abs(rect.bottom - point.y);\n    const right = Math.abs(rect.right - point.x);\n    const left = Math.abs(rect.left - point.x);\n    switch(Math.min(top, bottom, right, left)){\n        case left:\n            return \"left\";\n        case right:\n            return \"right\";\n        case top:\n            return \"top\";\n        case bottom:\n            return \"bottom\";\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n    const paddedExitPoints = [];\n    switch(exitSide){\n        case \"top\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"bottom\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            });\n            break;\n        case \"left\":\n            paddedExitPoints.push({\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"right\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            });\n            break;\n    }\n    return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n    const { top, right, bottom, left } = rect;\n    return [\n        {\n            x: left,\n            y: top\n        },\n        {\n            x: right,\n            y: top\n        },\n        {\n            x: right,\n            y: bottom\n        },\n        {\n            x: left,\n            y: bottom\n        }\n    ];\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const ii = polygon[i];\n        const jj = polygon[j];\n        const xi = ii.x;\n        const yi = ii.y;\n        const xj = jj.x;\n        const yj = jj.y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction getHull(points) {\n    const newPoints = points.slice();\n    newPoints.sort((a, b)=>{\n        if (a.x < b.x) return -1;\n        else if (a.x > b.x) return 1;\n        else if (a.y < b.y) return -1;\n        else if (a.y > b.y) return 1;\n        else return 0;\n    });\n    return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n    if (points.length <= 1) return points.slice();\n    const upperHull = [];\n    for(let i = 0; i < points.length; i++){\n        const p = points[i];\n        while(upperHull.length >= 2){\n            const q = upperHull[upperHull.length - 1];\n            const r = upperHull[upperHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n            else break;\n        }\n        upperHull.push(p);\n    }\n    upperHull.pop();\n    const lowerHull = [];\n    for(let i = points.length - 1; i >= 0; i--){\n        const p = points[i];\n        while(lowerHull.length >= 2){\n            const q = lowerHull[lowerHull.length - 1];\n            const r = lowerHull[lowerHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n            else break;\n        }\n        lowerHull.push(p);\n    }\n    lowerHull.pop();\n    if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n        return upperHull;\n    } else {\n        return upperHull.concat(lowerHull);\n    }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n    const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        callbackRef.current = callback;\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>callbackRef.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSwyREFBMkQ7QUFDNUI7QUFDL0IsU0FBU0MsZUFBZUMsUUFBUTtJQUM5QixNQUFNQyxjQUFjSCx5Q0FBWSxDQUFDRTtJQUNqQ0YsNENBQWUsQ0FBQztRQUNkRyxZQUFZRyxPQUFPLEdBQUdKO0lBQ3hCO0lBQ0EsT0FBT0YsMENBQWEsQ0FBQyxJQUFNLENBQUMsR0FBR1EsT0FBU0wsWUFBWUcsT0FBTyxNQUFNRSxPQUFPLEVBQUU7QUFDNUU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanM/MTFmMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtY2FsbGJhY2stcmVmL3NyYy91c2UtY2FsbGJhY2stcmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VDYWxsYmFja1JlZiIsImNhbGxiYWNrIiwiY2FsbGJhY2tSZWYiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJjdXJyZW50IiwidXNlTWVtbyIsImFyZ3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{}, caller }) {\n    const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== void 0;\n    const value = isControlled ? prop : uncontrolledProp;\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((nextValue)=>{\n        if (isControlled) {\n            const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n            if (value2 !== prop) {\n                onChangeRef.current?.(value2);\n            }\n        } else {\n            setUncontrolledProp(nextValue);\n        }\n    }, [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        onChangeRef\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n    const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n    useInsertionEffect(()=>{\n        onChangeRef.current = onChange;\n    }, [\n        onChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== value) {\n            onChangeRef.current?.(value);\n            prevValueRef.current = value;\n        }\n    }, [\n        value,\n        prevValueRef\n    ]);\n    return [\n        value,\n        setValue,\n        onChangeRef\n    ];\n}\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n    const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n    const isControlled = controlledState !== void 0;\n    const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const args = [\n        {\n            ...initialArg,\n            state: defaultProp\n        }\n    ];\n    if (init) {\n        args.push(init);\n    }\n    const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state2, action)=>{\n        if (action.type === SYNC_STATE) {\n            return {\n                ...state2,\n                state: action.state\n            };\n        }\n        const next = reducer(state2, action);\n        if (isControlled && !Object.is(next.state, state2.state)) {\n            onChange(next.state);\n        }\n        return next;\n    }, ...args);\n    const uncontrolledState = internalState.state;\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== uncontrolledState) {\n            prevValueRef.current = uncontrolledState;\n            if (!isControlled) {\n                onChange(uncontrolledState);\n            }\n        }\n    }, [\n        onChange,\n        uncontrolledState,\n        prevValueRef,\n        isControlled\n    ]);\n    const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const isControlled2 = controlledState !== void 0;\n        if (isControlled2) {\n            return {\n                ...internalState,\n                state: controlledState\n            };\n        }\n        return internalState;\n    }, [\n        internalState,\n        controlledState\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isControlled && !Object.is(controlledState, internalState.state)) {\n            dispatch({\n                type: SYNC_STATE,\n                state: controlledState\n            });\n        }\n    }, [\n        controlledState,\n        internalState.state,\n        isControlled\n    ]);\n    return [\n        state,\n        dispatch\n    ];\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n    if (typeof useReactEffectEvent === \"function\") {\n        return useReactEffectEvent(callback);\n    }\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{\n        throw new Error(\"Cannot call an event handler while rendering.\");\n    });\n    if (typeof useReactInsertionEffect === \"function\") {\n        useReactInsertionEffect(()=>{\n            ref.current = callback;\n        });\n    } else {\n        (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n            ref.current = callback;\n        });\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>ref.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n    const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            if (event.key === \"Escape\") {\n                onEscapeKeyDown(event);\n            }\n        };\n        ownerDocument.addEventListener(\"keydown\", handleKeyDown, {\n            capture: true\n        });\n        return ()=>ownerDocument.removeEventListener(\"keydown\", handleKeyDown, {\n                capture: true\n            });\n    }, [\n        onEscapeKeyDown,\n        ownerDocument\n    ]);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : ()=>{};\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsNkRBQTZEO0FBQzlCO0FBQy9CLElBQUlDLG1CQUFtQkMsWUFBWUMsV0FBV0gsa0RBQXFCLEdBQUcsS0FDdEU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzPzJkNmYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWxheW91dC1lZmZlY3Qvc3JjL3VzZS1sYXlvdXQtZWZmZWN0LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlTGF5b3V0RWZmZWN0MiA9IGdsb2JhbFRoaXM/LmRvY3VtZW50ID8gUmVhY3QudXNlTGF5b3V0RWZmZWN0IDogKCkgPT4ge1xufTtcbmV4cG9ydCB7XG4gIHVzZUxheW91dEVmZmVjdDIgYXMgdXNlTGF5b3V0RWZmZWN0XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlTGF5b3V0RWZmZWN0MiIsImdsb2JhbFRoaXMiLCJkb2N1bWVudCIsInVzZUxheW91dEVmZmVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-size/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSize: () => (/* binding */ useSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/use-size/src/use-size.tsx\n\n\nfunction useSize(element) {\n    const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(void 0);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if (element) {\n            setSize({\n                width: element.offsetWidth,\n                height: element.offsetHeight\n            });\n            const resizeObserver = new ResizeObserver((entries)=>{\n                if (!Array.isArray(entries)) {\n                    return;\n                }\n                if (!entries.length) {\n                    return;\n                }\n                const entry = entries[0];\n                let width;\n                let height;\n                if (\"borderBoxSize\" in entry) {\n                    const borderSizeEntry = entry[\"borderBoxSize\"];\n                    const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n                    width = borderSize[\"inlineSize\"];\n                    height = borderSize[\"blockSize\"];\n                } else {\n                    width = element.offsetWidth;\n                    height = element.offsetHeight;\n                }\n                setSize({\n                    width,\n                    height\n                });\n            });\n            resizeObserver.observe(element, {\n                box: \"border-box\"\n            });\n            return ()=>resizeObserver.unobserve(element);\n        } else {\n            setSize(void 0);\n        }\n    }, [\n        element\n    ]);\n    return size;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n    // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n    position: \"absolute\",\n    border: 0,\n    width: 1,\n    height: 1,\n    padding: 0,\n    margin: -1,\n    overflow: \"hidden\",\n    clip: \"rect(0, 0, 0, 0)\",\n    whiteSpace: \"nowrap\",\n    wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span, {\n        ...props,\n        ref: forwardedRef,\n        style: {\n            ...VISUALLY_HIDDEN_STYLES,\n            ...props.style\n        }\n    });\n});\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;