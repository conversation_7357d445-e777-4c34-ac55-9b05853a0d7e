"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-string";
exports.ids = ["vendor-chunks/mdast-util-to-string"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-string/lib/index.js":
/*!********************************************************!*\
  !*** ./node_modules/mdast-util-to-string/lib/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toString: () => (/* binding */ toString)\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Nodes} Nodes\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s (default: `true`).\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML (default: `true`).\n */ /** @type {Options} */ const emptyOptions = {};\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} [value]\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */ function toString(value, options) {\n    const settings = options || emptyOptions;\n    const includeImageAlt = typeof settings.includeImageAlt === \"boolean\" ? settings.includeImageAlt : true;\n    const includeHtml = typeof settings.includeHtml === \"boolean\" ? settings.includeHtml : true;\n    return one(value, includeImageAlt, includeHtml);\n}\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */ function one(value, includeImageAlt, includeHtml) {\n    if (node(value)) {\n        if (\"value\" in value) {\n            return value.type === \"html\" && !includeHtml ? \"\" : value.value;\n        }\n        if (includeImageAlt && \"alt\" in value && value.alt) {\n            return value.alt;\n        }\n        if (\"children\" in value) {\n            return all(value.children, includeImageAlt, includeHtml);\n        }\n    }\n    if (Array.isArray(value)) {\n        return all(value, includeImageAlt, includeHtml);\n    }\n    return \"\";\n}\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */ function all(values, includeImageAlt, includeHtml) {\n    /** @type {Array<string>} */ const result = [];\n    let index = -1;\n    while(++index < values.length){\n        result[index] = one(values[index], includeImageAlt, includeHtml);\n    }\n    return result.join(\"\");\n}\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Nodes}\n *   Whether `value` is a node.\n */ function node(value) {\n    return Boolean(value && typeof value === \"object\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-string/lib/index.js\n");

/***/ })

};
;