"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tailwind-merge";
exports.ids = ["vendor-chunks/tailwind-merge"];
exports.modules = {

/***/ "(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/bundle-mjs.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTailwindMerge: () => (/* binding */ createTailwindMerge),\n/* harmony export */   extendTailwindMerge: () => (/* binding */ extendTailwindMerge),\n/* harmony export */   fromTheme: () => (/* binding */ fromTheme),\n/* harmony export */   getDefaultConfig: () => (/* binding */ getDefaultConfig),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   twJoin: () => (/* binding */ twJoin),\n/* harmony export */   twMerge: () => (/* binding */ twMerge),\n/* harmony export */   validators: () => (/* binding */ validators)\n/* harmony export */ });\nconst CLASS_PART_SEPARATOR = \"-\";\nconst createClassGroupUtils = (config)=>{\n    const classMap = createClassMap(config);\n    const { conflictingClassGroups, conflictingClassGroupModifiers } = config;\n    const getClassGroupId = (className)=>{\n        const classParts = className.split(CLASS_PART_SEPARATOR);\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === \"\" && classParts.length !== 1) {\n            classParts.shift();\n        }\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n    };\n    const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier)=>{\n        const conflicts = conflictingClassGroups[classGroupId] || [];\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [\n                ...conflicts,\n                ...conflictingClassGroupModifiers[classGroupId]\n            ];\n        }\n        return conflicts;\n    };\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds\n    };\n};\nconst getGroupRecursive = (classParts, classPartObject)=>{\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId;\n    }\n    const currentClassPart = classParts[0];\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n    const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart;\n    }\n    if (classPartObject.validators.length === 0) {\n        return undefined;\n    }\n    const classRest = classParts.join(CLASS_PART_SEPARATOR);\n    return classPartObject.validators.find(({ validator })=>validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = (className)=>{\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n        const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(\":\"));\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return \"arbitrary..\" + property;\n        }\n    }\n};\n/**\n * Exported for testing only\n */ const createClassMap = (config)=>{\n    const { theme, classGroups } = config;\n    const classMap = {\n        nextPart: new Map(),\n        validators: []\n    };\n    for(const classGroupId in classGroups){\n        processClassesRecursively(classGroups[classGroupId], classMap, classGroupId, theme);\n    }\n    return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme)=>{\n    classGroup.forEach((classDefinition)=>{\n        if (typeof classDefinition === \"string\") {\n            const classPartObjectToEdit = classDefinition === \"\" ? classPartObject : getPart(classPartObject, classDefinition);\n            classPartObjectToEdit.classGroupId = classGroupId;\n            return;\n        }\n        if (typeof classDefinition === \"function\") {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n                return;\n            }\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId\n            });\n            return;\n        }\n        Object.entries(classDefinition).forEach(([key, classGroup])=>{\n            processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n        });\n    });\n};\nconst getPart = (classPartObject, path)=>{\n    let currentClassPartObject = classPartObject;\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart)=>{\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: []\n            });\n        }\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n    });\n    return currentClassPartObject;\n};\nconst isThemeGetter = (func)=>func.isThemeGetter;\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = (maxCacheSize)=>{\n    if (maxCacheSize < 1) {\n        return {\n            get: ()=>undefined,\n            set: ()=>{}\n        };\n    }\n    let cacheSize = 0;\n    let cache = new Map();\n    let previousCache = new Map();\n    const update = (key, value)=>{\n        cache.set(key, value);\n        cacheSize++;\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0;\n            previousCache = cache;\n            cache = new Map();\n        }\n    };\n    return {\n        get (key) {\n            let value = cache.get(key);\n            if (value !== undefined) {\n                return value;\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value);\n                return value;\n            }\n        },\n        set (key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value);\n            } else {\n                update(key, value);\n            }\n        }\n    };\n};\nconst IMPORTANT_MODIFIER = \"!\";\nconst MODIFIER_SEPARATOR = \":\";\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length;\nconst createParseClassName = (config)=>{\n    const { prefix, experimentalParseClassName } = config;\n    /**\n   * Parse class name into parts.\n   *\n   * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n   * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n   */ let parseClassName = (className)=>{\n        const modifiers = [];\n        let bracketDepth = 0;\n        let parenDepth = 0;\n        let modifierStart = 0;\n        let postfixModifierPosition;\n        for(let index = 0; index < className.length; index++){\n            let currentCharacter = className[index];\n            if (bracketDepth === 0 && parenDepth === 0) {\n                if (currentCharacter === MODIFIER_SEPARATOR) {\n                    modifiers.push(className.slice(modifierStart, index));\n                    modifierStart = index + MODIFIER_SEPARATOR_LENGTH;\n                    continue;\n                }\n                if (currentCharacter === \"/\") {\n                    postfixModifierPosition = index;\n                    continue;\n                }\n            }\n            if (currentCharacter === \"[\") {\n                bracketDepth++;\n            } else if (currentCharacter === \"]\") {\n                bracketDepth--;\n            } else if (currentCharacter === \"(\") {\n                parenDepth++;\n            } else if (currentCharacter === \")\") {\n                parenDepth--;\n            }\n        }\n        const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n        const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier);\n        const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier;\n        const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition\n        };\n    };\n    if (prefix) {\n        const fullPrefix = prefix + MODIFIER_SEPARATOR;\n        const parseClassNameOriginal = parseClassName;\n        parseClassName = (className)=>className.startsWith(fullPrefix) ? parseClassNameOriginal(className.substring(fullPrefix.length)) : {\n                isExternal: true,\n                modifiers: [],\n                hasImportantModifier: false,\n                baseClassName: className,\n                maybePostfixModifierPosition: undefined\n            };\n    }\n    if (experimentalParseClassName) {\n        const parseClassNameOriginal = parseClassName;\n        parseClassName = (className)=>experimentalParseClassName({\n                className,\n                parseClassName: parseClassNameOriginal\n            });\n    }\n    return parseClassName;\n};\nconst stripImportantModifier = (baseClassName)=>{\n    if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(0, baseClassName.length - 1);\n    }\n    /**\n   * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n   * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n   */ if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(1);\n    }\n    return baseClassName;\n};\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */ const createSortModifiers = (config)=>{\n    const orderSensitiveModifiers = Object.fromEntries(config.orderSensitiveModifiers.map((modifier)=>[\n            modifier,\n            true\n        ]));\n    const sortModifiers = (modifiers)=>{\n        if (modifiers.length <= 1) {\n            return modifiers;\n        }\n        const sortedModifiers = [];\n        let unsortedModifiers = [];\n        modifiers.forEach((modifier)=>{\n            const isPositionSensitive = modifier[0] === \"[\" || orderSensitiveModifiers[modifier];\n            if (isPositionSensitive) {\n                sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n                unsortedModifiers = [];\n            } else {\n                unsortedModifiers.push(modifier);\n            }\n        });\n        sortedModifiers.push(...unsortedModifiers.sort());\n        return sortedModifiers;\n    };\n    return sortModifiers;\n};\nconst createConfigUtils = (config)=>({\n        cache: createLruCache(config.cacheSize),\n        parseClassName: createParseClassName(config),\n        sortModifiers: createSortModifiers(config),\n        ...createClassGroupUtils(config)\n    });\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils)=>{\n    const { parseClassName, getClassGroupId, getConflictingClassGroupIds, sortModifiers } = configUtils;\n    /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */ const classGroupsInConflict = [];\n    const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n    let result = \"\";\n    for(let index = classNames.length - 1; index >= 0; index -= 1){\n        const originalClassName = classNames[index];\n        const { isExternal, modifiers, hasImportantModifier, baseClassName, maybePostfixModifierPosition } = parseClassName(originalClassName);\n        if (isExternal) {\n            result = originalClassName + (result.length > 0 ? \" \" + result : result);\n            continue;\n        }\n        let hasPostfixModifier = !!maybePostfixModifierPosition;\n        let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n        if (!classGroupId) {\n            if (!hasPostfixModifier) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? \" \" + result : result);\n                continue;\n            }\n            classGroupId = getClassGroupId(baseClassName);\n            if (!classGroupId) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? \" \" + result : result);\n                continue;\n            }\n            hasPostfixModifier = false;\n        }\n        const variantModifier = sortModifiers(modifiers).join(\":\");\n        const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n        const classId = modifierId + classGroupId;\n        if (classGroupsInConflict.includes(classId)) {\n            continue;\n        }\n        classGroupsInConflict.push(classId);\n        const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n        for(let i = 0; i < conflictGroups.length; ++i){\n            const group = conflictGroups[i];\n            classGroupsInConflict.push(modifierId + group);\n        }\n        // Tailwind class not in conflict\n        result = originalClassName + (result.length > 0 ? \" \" + result : result);\n    }\n    return result;\n};\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */ function twJoin() {\n    let index = 0;\n    let argument;\n    let resolvedValue;\n    let string = \"\";\n    while(index < arguments.length){\n        if (argument = arguments[index++]) {\n            if (resolvedValue = toValue(argument)) {\n                string && (string += \" \");\n                string += resolvedValue;\n            }\n        }\n    }\n    return string;\n}\nconst toValue = (mix)=>{\n    if (typeof mix === \"string\") {\n        return mix;\n    }\n    let resolvedValue;\n    let string = \"\";\n    for(let k = 0; k < mix.length; k++){\n        if (mix[k]) {\n            if (resolvedValue = toValue(mix[k])) {\n                string && (string += \" \");\n                string += resolvedValue;\n            }\n        }\n    }\n    return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n    let configUtils;\n    let cacheGet;\n    let cacheSet;\n    let functionToCall = initTailwindMerge;\n    function initTailwindMerge(classList) {\n        const config = createConfigRest.reduce((previousConfig, createConfigCurrent)=>createConfigCurrent(previousConfig), createConfigFirst());\n        configUtils = createConfigUtils(config);\n        cacheGet = configUtils.cache.get;\n        cacheSet = configUtils.cache.set;\n        functionToCall = tailwindMerge;\n        return tailwindMerge(classList);\n    }\n    function tailwindMerge(classList) {\n        const cachedResult = cacheGet(classList);\n        if (cachedResult) {\n            return cachedResult;\n        }\n        const result = mergeClassList(classList, configUtils);\n        cacheSet(classList, result);\n        return result;\n    }\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments));\n    };\n}\nconst fromTheme = (key)=>{\n    const themeGetter = (theme)=>theme[key] || [];\n    themeGetter.isThemeGetter = true;\n    return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i;\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isFraction = (value)=>fractionRegex.test(value);\nconst isNumber = (value)=>!!value && !Number.isNaN(Number(value));\nconst isInteger = (value)=>!!value && Number.isInteger(Number(value));\nconst isPercent = (value)=>value.endsWith(\"%\") && isNumber(value.slice(0, -1));\nconst isTshirtSize = (value)=>tshirtUnitRegex.test(value);\nconst isAny = ()=>true;\nconst isLengthOnly = (value)=>// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n    // For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n    // I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\n    lengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = ()=>false;\nconst isShadow = (value)=>shadowRegex.test(value);\nconst isImage = (value)=>imageRegex.test(value);\nconst isAnyNonArbitrary = (value)=>!isArbitraryValue(value) && !isArbitraryVariable(value);\nconst isArbitrarySize = (value)=>getIsArbitraryValue(value, isLabelSize, isNever);\nconst isArbitraryValue = (value)=>arbitraryValueRegex.test(value);\nconst isArbitraryLength = (value)=>getIsArbitraryValue(value, isLabelLength, isLengthOnly);\nconst isArbitraryNumber = (value)=>getIsArbitraryValue(value, isLabelNumber, isNumber);\nconst isArbitraryPosition = (value)=>getIsArbitraryValue(value, isLabelPosition, isNever);\nconst isArbitraryImage = (value)=>getIsArbitraryValue(value, isLabelImage, isImage);\nconst isArbitraryShadow = (value)=>getIsArbitraryValue(value, isLabelShadow, isShadow);\nconst isArbitraryVariable = (value)=>arbitraryVariableRegex.test(value);\nconst isArbitraryVariableLength = (value)=>getIsArbitraryVariable(value, isLabelLength);\nconst isArbitraryVariableFamilyName = (value)=>getIsArbitraryVariable(value, isLabelFamilyName);\nconst isArbitraryVariablePosition = (value)=>getIsArbitraryVariable(value, isLabelPosition);\nconst isArbitraryVariableSize = (value)=>getIsArbitraryVariable(value, isLabelSize);\nconst isArbitraryVariableImage = (value)=>getIsArbitraryVariable(value, isLabelImage);\nconst isArbitraryVariableShadow = (value)=>getIsArbitraryVariable(value, isLabelShadow, true);\n// Helpers\nconst getIsArbitraryValue = (value, testLabel, testValue)=>{\n    const result = arbitraryValueRegex.exec(value);\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1]);\n        }\n        return testValue(result[2]);\n    }\n    return false;\n};\nconst getIsArbitraryVariable = (value, testLabel, shouldMatchNoLabel = false)=>{\n    const result = arbitraryVariableRegex.exec(value);\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1]);\n        }\n        return shouldMatchNoLabel;\n    }\n    return false;\n};\n// Labels\nconst isLabelPosition = (label)=>label === \"position\" || label === \"percentage\";\nconst isLabelImage = (label)=>label === \"image\" || label === \"url\";\nconst isLabelSize = (label)=>label === \"length\" || label === \"size\" || label === \"bg-size\";\nconst isLabelLength = (label)=>label === \"length\";\nconst isLabelNumber = (label)=>label === \"number\";\nconst isLabelFamilyName = (label)=>label === \"family-name\";\nconst isLabelShadow = (label)=>label === \"shadow\";\nconst validators = /*#__PURE__*/ Object.defineProperty({\n    __proto__: null,\n    isAny,\n    isAnyNonArbitrary,\n    isArbitraryImage,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryValue,\n    isArbitraryVariable,\n    isArbitraryVariableFamilyName,\n    isArbitraryVariableImage,\n    isArbitraryVariableLength,\n    isArbitraryVariablePosition,\n    isArbitraryVariableShadow,\n    isArbitraryVariableSize,\n    isFraction,\n    isInteger,\n    isNumber,\n    isPercent,\n    isTshirtSize\n}, Symbol.toStringTag, {\n    value: \"Module\"\n});\nconst getDefaultConfig = ()=>{\n    /**\n   * Theme getters for theme variable namespaces\n   * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n   */ /***/ const themeColor = fromTheme(\"color\");\n    const themeFont = fromTheme(\"font\");\n    const themeText = fromTheme(\"text\");\n    const themeFontWeight = fromTheme(\"font-weight\");\n    const themeTracking = fromTheme(\"tracking\");\n    const themeLeading = fromTheme(\"leading\");\n    const themeBreakpoint = fromTheme(\"breakpoint\");\n    const themeContainer = fromTheme(\"container\");\n    const themeSpacing = fromTheme(\"spacing\");\n    const themeRadius = fromTheme(\"radius\");\n    const themeShadow = fromTheme(\"shadow\");\n    const themeInsetShadow = fromTheme(\"inset-shadow\");\n    const themeTextShadow = fromTheme(\"text-shadow\");\n    const themeDropShadow = fromTheme(\"drop-shadow\");\n    const themeBlur = fromTheme(\"blur\");\n    const themePerspective = fromTheme(\"perspective\");\n    const themeAspect = fromTheme(\"aspect\");\n    const themeEase = fromTheme(\"ease\");\n    const themeAnimate = fromTheme(\"animate\");\n    /**\n   * Helpers to avoid repeating the same scales\n   *\n   * We use functions that create a new array every time they're called instead of static arrays.\n   * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n   */ /***/ const scaleBreak = ()=>[\n            \"auto\",\n            \"avoid\",\n            \"all\",\n            \"avoid-page\",\n            \"page\",\n            \"left\",\n            \"right\",\n            \"column\"\n        ];\n    const scalePosition = ()=>[\n            \"center\",\n            \"top\",\n            \"bottom\",\n            \"left\",\n            \"right\",\n            \"top-left\",\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            \"left-top\",\n            \"top-right\",\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            \"right-top\",\n            \"bottom-right\",\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            \"right-bottom\",\n            \"bottom-left\",\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            \"left-bottom\"\n        ];\n    const scalePositionWithArbitrary = ()=>[\n            ...scalePosition(),\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleOverflow = ()=>[\n            \"auto\",\n            \"hidden\",\n            \"clip\",\n            \"visible\",\n            \"scroll\"\n        ];\n    const scaleOverscroll = ()=>[\n            \"auto\",\n            \"contain\",\n            \"none\"\n        ];\n    const scaleUnambiguousSpacing = ()=>[\n            isArbitraryVariable,\n            isArbitraryValue,\n            themeSpacing\n        ];\n    const scaleInset = ()=>[\n            isFraction,\n            \"full\",\n            \"auto\",\n            ...scaleUnambiguousSpacing()\n        ];\n    const scaleGridTemplateColsRows = ()=>[\n            isInteger,\n            \"none\",\n            \"subgrid\",\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleGridColRowStartAndEnd = ()=>[\n            \"auto\",\n            {\n                span: [\n                    \"full\",\n                    isInteger,\n                    isArbitraryVariable,\n                    isArbitraryValue\n                ]\n            },\n            isInteger,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleGridColRowStartOrEnd = ()=>[\n            isInteger,\n            \"auto\",\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleGridAutoColsRows = ()=>[\n            \"auto\",\n            \"min\",\n            \"max\",\n            \"fr\",\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleAlignPrimaryAxis = ()=>[\n            \"start\",\n            \"end\",\n            \"center\",\n            \"between\",\n            \"around\",\n            \"evenly\",\n            \"stretch\",\n            \"baseline\",\n            \"center-safe\",\n            \"end-safe\"\n        ];\n    const scaleAlignSecondaryAxis = ()=>[\n            \"start\",\n            \"end\",\n            \"center\",\n            \"stretch\",\n            \"center-safe\",\n            \"end-safe\"\n        ];\n    const scaleMargin = ()=>[\n            \"auto\",\n            ...scaleUnambiguousSpacing()\n        ];\n    const scaleSizing = ()=>[\n            isFraction,\n            \"auto\",\n            \"full\",\n            \"dvw\",\n            \"dvh\",\n            \"lvw\",\n            \"lvh\",\n            \"svw\",\n            \"svh\",\n            \"min\",\n            \"max\",\n            \"fit\",\n            ...scaleUnambiguousSpacing()\n        ];\n    const scaleColor = ()=>[\n            themeColor,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleBgPosition = ()=>[\n            ...scalePosition(),\n            isArbitraryVariablePosition,\n            isArbitraryPosition,\n            {\n                position: [\n                    isArbitraryVariable,\n                    isArbitraryValue\n                ]\n            }\n        ];\n    const scaleBgRepeat = ()=>[\n            \"no-repeat\",\n            {\n                repeat: [\n                    \"\",\n                    \"x\",\n                    \"y\",\n                    \"space\",\n                    \"round\"\n                ]\n            }\n        ];\n    const scaleBgSize = ()=>[\n            \"auto\",\n            \"cover\",\n            \"contain\",\n            isArbitraryVariableSize,\n            isArbitrarySize,\n            {\n                size: [\n                    isArbitraryVariable,\n                    isArbitraryValue\n                ]\n            }\n        ];\n    const scaleGradientStopPosition = ()=>[\n            isPercent,\n            isArbitraryVariableLength,\n            isArbitraryLength\n        ];\n    const scaleRadius = ()=>[\n            // Deprecated since Tailwind CSS v4.0.0\n            \"\",\n            \"none\",\n            \"full\",\n            themeRadius,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleBorderWidth = ()=>[\n            \"\",\n            isNumber,\n            isArbitraryVariableLength,\n            isArbitraryLength\n        ];\n    const scaleLineStyle = ()=>[\n            \"solid\",\n            \"dashed\",\n            \"dotted\",\n            \"double\"\n        ];\n    const scaleBlendMode = ()=>[\n            \"normal\",\n            \"multiply\",\n            \"screen\",\n            \"overlay\",\n            \"darken\",\n            \"lighten\",\n            \"color-dodge\",\n            \"color-burn\",\n            \"hard-light\",\n            \"soft-light\",\n            \"difference\",\n            \"exclusion\",\n            \"hue\",\n            \"saturation\",\n            \"color\",\n            \"luminosity\"\n        ];\n    const scaleMaskImagePosition = ()=>[\n            isNumber,\n            isPercent,\n            isArbitraryVariablePosition,\n            isArbitraryPosition\n        ];\n    const scaleBlur = ()=>[\n            // Deprecated since Tailwind CSS v4.0.0\n            \"\",\n            \"none\",\n            themeBlur,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleRotate = ()=>[\n            \"none\",\n            isNumber,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleScale = ()=>[\n            \"none\",\n            isNumber,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleSkew = ()=>[\n            isNumber,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleTranslate = ()=>[\n            isFraction,\n            \"full\",\n            ...scaleUnambiguousSpacing()\n        ];\n    return {\n        cacheSize: 500,\n        theme: {\n            animate: [\n                \"spin\",\n                \"ping\",\n                \"pulse\",\n                \"bounce\"\n            ],\n            aspect: [\n                \"video\"\n            ],\n            blur: [\n                isTshirtSize\n            ],\n            breakpoint: [\n                isTshirtSize\n            ],\n            color: [\n                isAny\n            ],\n            container: [\n                isTshirtSize\n            ],\n            \"drop-shadow\": [\n                isTshirtSize\n            ],\n            ease: [\n                \"in\",\n                \"out\",\n                \"in-out\"\n            ],\n            font: [\n                isAnyNonArbitrary\n            ],\n            \"font-weight\": [\n                \"thin\",\n                \"extralight\",\n                \"light\",\n                \"normal\",\n                \"medium\",\n                \"semibold\",\n                \"bold\",\n                \"extrabold\",\n                \"black\"\n            ],\n            \"inset-shadow\": [\n                isTshirtSize\n            ],\n            leading: [\n                \"none\",\n                \"tight\",\n                \"snug\",\n                \"normal\",\n                \"relaxed\",\n                \"loose\"\n            ],\n            perspective: [\n                \"dramatic\",\n                \"near\",\n                \"normal\",\n                \"midrange\",\n                \"distant\",\n                \"none\"\n            ],\n            radius: [\n                isTshirtSize\n            ],\n            shadow: [\n                isTshirtSize\n            ],\n            spacing: [\n                \"px\",\n                isNumber\n            ],\n            text: [\n                isTshirtSize\n            ],\n            \"text-shadow\": [\n                isTshirtSize\n            ],\n            tracking: [\n                \"tighter\",\n                \"tight\",\n                \"normal\",\n                \"wide\",\n                \"wider\",\n                \"widest\"\n            ]\n        },\n        classGroups: {\n            // --------------\n            // --- Layout ---\n            // --------------\n            /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */ aspect: [\n                {\n                    aspect: [\n                        \"auto\",\n                        \"square\",\n                        isFraction,\n                        isArbitraryValue,\n                        isArbitraryVariable,\n                        themeAspect\n                    ]\n                }\n            ],\n            /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       * @deprecated since Tailwind CSS v4.0.0\n       */ container: [\n                \"container\"\n            ],\n            /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */ columns: [\n                {\n                    columns: [\n                        isNumber,\n                        isArbitraryValue,\n                        isArbitraryVariable,\n                        themeContainer\n                    ]\n                }\n            ],\n            /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */ \"break-after\": [\n                {\n                    \"break-after\": scaleBreak()\n                }\n            ],\n            /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */ \"break-before\": [\n                {\n                    \"break-before\": scaleBreak()\n                }\n            ],\n            /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */ \"break-inside\": [\n                {\n                    \"break-inside\": [\n                        \"auto\",\n                        \"avoid\",\n                        \"avoid-page\",\n                        \"avoid-column\"\n                    ]\n                }\n            ],\n            /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */ \"box-decoration\": [\n                {\n                    \"box-decoration\": [\n                        \"slice\",\n                        \"clone\"\n                    ]\n                }\n            ],\n            /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */ box: [\n                {\n                    box: [\n                        \"border\",\n                        \"content\"\n                    ]\n                }\n            ],\n            /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */ display: [\n                \"block\",\n                \"inline-block\",\n                \"inline\",\n                \"flex\",\n                \"inline-flex\",\n                \"table\",\n                \"inline-table\",\n                \"table-caption\",\n                \"table-cell\",\n                \"table-column\",\n                \"table-column-group\",\n                \"table-footer-group\",\n                \"table-header-group\",\n                \"table-row-group\",\n                \"table-row\",\n                \"flow-root\",\n                \"grid\",\n                \"inline-grid\",\n                \"contents\",\n                \"list-item\",\n                \"hidden\"\n            ],\n            /**\n       * Screen Reader Only\n       * @see https://tailwindcss.com/docs/display#screen-reader-only\n       */ sr: [\n                \"sr-only\",\n                \"not-sr-only\"\n            ],\n            /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */ float: [\n                {\n                    float: [\n                        \"right\",\n                        \"left\",\n                        \"none\",\n                        \"start\",\n                        \"end\"\n                    ]\n                }\n            ],\n            /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */ clear: [\n                {\n                    clear: [\n                        \"left\",\n                        \"right\",\n                        \"both\",\n                        \"none\",\n                        \"start\",\n                        \"end\"\n                    ]\n                }\n            ],\n            /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */ isolation: [\n                \"isolate\",\n                \"isolation-auto\"\n            ],\n            /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */ \"object-fit\": [\n                {\n                    object: [\n                        \"contain\",\n                        \"cover\",\n                        \"fill\",\n                        \"none\",\n                        \"scale-down\"\n                    ]\n                }\n            ],\n            /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */ \"object-position\": [\n                {\n                    object: scalePositionWithArbitrary()\n                }\n            ],\n            /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */ overflow: [\n                {\n                    overflow: scaleOverflow()\n                }\n            ],\n            /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */ \"overflow-x\": [\n                {\n                    \"overflow-x\": scaleOverflow()\n                }\n            ],\n            /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */ \"overflow-y\": [\n                {\n                    \"overflow-y\": scaleOverflow()\n                }\n            ],\n            /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ overscroll: [\n                {\n                    overscroll: scaleOverscroll()\n                }\n            ],\n            /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ \"overscroll-x\": [\n                {\n                    \"overscroll-x\": scaleOverscroll()\n                }\n            ],\n            /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ \"overscroll-y\": [\n                {\n                    \"overscroll-y\": scaleOverscroll()\n                }\n            ],\n            /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */ position: [\n                \"static\",\n                \"fixed\",\n                \"absolute\",\n                \"relative\",\n                \"sticky\"\n            ],\n            /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ inset: [\n                {\n                    inset: scaleInset()\n                }\n            ],\n            /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ \"inset-x\": [\n                {\n                    \"inset-x\": scaleInset()\n                }\n            ],\n            /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ \"inset-y\": [\n                {\n                    \"inset-y\": scaleInset()\n                }\n            ],\n            /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ start: [\n                {\n                    start: scaleInset()\n                }\n            ],\n            /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ end: [\n                {\n                    end: scaleInset()\n                }\n            ],\n            /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ top: [\n                {\n                    top: scaleInset()\n                }\n            ],\n            /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ right: [\n                {\n                    right: scaleInset()\n                }\n            ],\n            /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ bottom: [\n                {\n                    bottom: scaleInset()\n                }\n            ],\n            /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ left: [\n                {\n                    left: scaleInset()\n                }\n            ],\n            /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */ visibility: [\n                \"visible\",\n                \"invisible\",\n                \"collapse\"\n            ],\n            /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */ z: [\n                {\n                    z: [\n                        isInteger,\n                        \"auto\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // ------------------------\n            // --- Flexbox and Grid ---\n            // ------------------------\n            /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */ basis: [\n                {\n                    basis: [\n                        isFraction,\n                        \"full\",\n                        \"auto\",\n                        themeContainer,\n                        ...scaleUnambiguousSpacing()\n                    ]\n                }\n            ],\n            /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */ \"flex-direction\": [\n                {\n                    flex: [\n                        \"row\",\n                        \"row-reverse\",\n                        \"col\",\n                        \"col-reverse\"\n                    ]\n                }\n            ],\n            /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */ \"flex-wrap\": [\n                {\n                    flex: [\n                        \"nowrap\",\n                        \"wrap\",\n                        \"wrap-reverse\"\n                    ]\n                }\n            ],\n            /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */ flex: [\n                {\n                    flex: [\n                        isNumber,\n                        isFraction,\n                        \"auto\",\n                        \"initial\",\n                        \"none\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */ grow: [\n                {\n                    grow: [\n                        \"\",\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */ shrink: [\n                {\n                    shrink: [\n                        \"\",\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */ order: [\n                {\n                    order: [\n                        isInteger,\n                        \"first\",\n                        \"last\",\n                        \"none\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */ \"grid-cols\": [\n                {\n                    \"grid-cols\": scaleGridTemplateColsRows()\n                }\n            ],\n            /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */ \"col-start-end\": [\n                {\n                    col: scaleGridColRowStartAndEnd()\n                }\n            ],\n            /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */ \"col-start\": [\n                {\n                    \"col-start\": scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */ \"col-end\": [\n                {\n                    \"col-end\": scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */ \"grid-rows\": [\n                {\n                    \"grid-rows\": scaleGridTemplateColsRows()\n                }\n            ],\n            /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */ \"row-start-end\": [\n                {\n                    row: scaleGridColRowStartAndEnd()\n                }\n            ],\n            /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */ \"row-start\": [\n                {\n                    \"row-start\": scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */ \"row-end\": [\n                {\n                    \"row-end\": scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */ \"grid-flow\": [\n                {\n                    \"grid-flow\": [\n                        \"row\",\n                        \"col\",\n                        \"dense\",\n                        \"row-dense\",\n                        \"col-dense\"\n                    ]\n                }\n            ],\n            /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */ \"auto-cols\": [\n                {\n                    \"auto-cols\": scaleGridAutoColsRows()\n                }\n            ],\n            /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */ \"auto-rows\": [\n                {\n                    \"auto-rows\": scaleGridAutoColsRows()\n                }\n            ],\n            /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */ gap: [\n                {\n                    gap: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */ \"gap-x\": [\n                {\n                    \"gap-x\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */ \"gap-y\": [\n                {\n                    \"gap-y\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */ \"justify-content\": [\n                {\n                    justify: [\n                        ...scaleAlignPrimaryAxis(),\n                        \"normal\"\n                    ]\n                }\n            ],\n            /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */ \"justify-items\": [\n                {\n                    \"justify-items\": [\n                        ...scaleAlignSecondaryAxis(),\n                        \"normal\"\n                    ]\n                }\n            ],\n            /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */ \"justify-self\": [\n                {\n                    \"justify-self\": [\n                        \"auto\",\n                        ...scaleAlignSecondaryAxis()\n                    ]\n                }\n            ],\n            /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */ \"align-content\": [\n                {\n                    content: [\n                        \"normal\",\n                        ...scaleAlignPrimaryAxis()\n                    ]\n                }\n            ],\n            /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */ \"align-items\": [\n                {\n                    items: [\n                        ...scaleAlignSecondaryAxis(),\n                        {\n                            baseline: [\n                                \"\",\n                                \"last\"\n                            ]\n                        }\n                    ]\n                }\n            ],\n            /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */ \"align-self\": [\n                {\n                    self: [\n                        \"auto\",\n                        ...scaleAlignSecondaryAxis(),\n                        {\n                            baseline: [\n                                \"\",\n                                \"last\"\n                            ]\n                        }\n                    ]\n                }\n            ],\n            /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */ \"place-content\": [\n                {\n                    \"place-content\": scaleAlignPrimaryAxis()\n                }\n            ],\n            /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */ \"place-items\": [\n                {\n                    \"place-items\": [\n                        ...scaleAlignSecondaryAxis(),\n                        \"baseline\"\n                    ]\n                }\n            ],\n            /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */ \"place-self\": [\n                {\n                    \"place-self\": [\n                        \"auto\",\n                        ...scaleAlignSecondaryAxis()\n                    ]\n                }\n            ],\n            // Spacing\n            /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */ p: [\n                {\n                    p: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */ px: [\n                {\n                    px: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */ py: [\n                {\n                    py: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */ ps: [\n                {\n                    ps: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */ pe: [\n                {\n                    pe: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */ pt: [\n                {\n                    pt: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */ pr: [\n                {\n                    pr: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */ pb: [\n                {\n                    pb: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */ pl: [\n                {\n                    pl: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */ m: [\n                {\n                    m: scaleMargin()\n                }\n            ],\n            /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */ mx: [\n                {\n                    mx: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */ my: [\n                {\n                    my: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */ ms: [\n                {\n                    ms: scaleMargin()\n                }\n            ],\n            /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */ me: [\n                {\n                    me: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */ mt: [\n                {\n                    mt: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */ mr: [\n                {\n                    mr: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */ mb: [\n                {\n                    mb: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */ ml: [\n                {\n                    ml: scaleMargin()\n                }\n            ],\n            /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ \"space-x\": [\n                {\n                    \"space-x\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ \"space-x-reverse\": [\n                \"space-x-reverse\"\n            ],\n            /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ \"space-y\": [\n                {\n                    \"space-y\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ \"space-y-reverse\": [\n                \"space-y-reverse\"\n            ],\n            // --------------\n            // --- Sizing ---\n            // --------------\n            /**\n       * Size\n       * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n       */ size: [\n                {\n                    size: scaleSizing()\n                }\n            ],\n            /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */ w: [\n                {\n                    w: [\n                        themeContainer,\n                        \"screen\",\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */ \"min-w\": [\n                {\n                    \"min-w\": [\n                        themeContainer,\n                        \"screen\",\n                        /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ \"none\",\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */ \"max-w\": [\n                {\n                    \"max-w\": [\n                        themeContainer,\n                        \"screen\",\n                        \"none\",\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ \"prose\",\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ {\n                            screen: [\n                                themeBreakpoint\n                            ]\n                        },\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */ h: [\n                {\n                    h: [\n                        \"screen\",\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */ \"min-h\": [\n                {\n                    \"min-h\": [\n                        \"screen\",\n                        \"none\",\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */ \"max-h\": [\n                {\n                    \"max-h\": [\n                        \"screen\",\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            // ------------------\n            // --- Typography ---\n            // ------------------\n            /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */ \"font-size\": [\n                {\n                    text: [\n                        \"base\",\n                        themeText,\n                        isArbitraryVariableLength,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */ \"font-smoothing\": [\n                \"antialiased\",\n                \"subpixel-antialiased\"\n            ],\n            /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */ \"font-style\": [\n                \"italic\",\n                \"not-italic\"\n            ],\n            /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */ \"font-weight\": [\n                {\n                    font: [\n                        themeFontWeight,\n                        isArbitraryVariable,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Font Stretch\n       * @see https://tailwindcss.com/docs/font-stretch\n       */ \"font-stretch\": [\n                {\n                    \"font-stretch\": [\n                        \"ultra-condensed\",\n                        \"extra-condensed\",\n                        \"condensed\",\n                        \"semi-condensed\",\n                        \"normal\",\n                        \"semi-expanded\",\n                        \"expanded\",\n                        \"extra-expanded\",\n                        \"ultra-expanded\",\n                        isPercent,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */ \"font-family\": [\n                {\n                    font: [\n                        isArbitraryVariableFamilyName,\n                        isArbitraryValue,\n                        themeFont\n                    ]\n                }\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-normal\": [\n                \"normal-nums\"\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-ordinal\": [\n                \"ordinal\"\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-slashed-zero\": [\n                \"slashed-zero\"\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-figure\": [\n                \"lining-nums\",\n                \"oldstyle-nums\"\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-spacing\": [\n                \"proportional-nums\",\n                \"tabular-nums\"\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-fraction\": [\n                \"diagonal-fractions\",\n                \"stacked-fractions\"\n            ],\n            /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */ tracking: [\n                {\n                    tracking: [\n                        themeTracking,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */ \"line-clamp\": [\n                {\n                    \"line-clamp\": [\n                        isNumber,\n                        \"none\",\n                        isArbitraryVariable,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */ leading: [\n                {\n                    leading: [\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ themeLeading,\n                        ...scaleUnambiguousSpacing()\n                    ]\n                }\n            ],\n            /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */ \"list-image\": [\n                {\n                    \"list-image\": [\n                        \"none\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */ \"list-style-position\": [\n                {\n                    list: [\n                        \"inside\",\n                        \"outside\"\n                    ]\n                }\n            ],\n            /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */ \"list-style-type\": [\n                {\n                    list: [\n                        \"disc\",\n                        \"decimal\",\n                        \"none\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */ \"text-alignment\": [\n                {\n                    text: [\n                        \"left\",\n                        \"center\",\n                        \"right\",\n                        \"justify\",\n                        \"start\",\n                        \"end\"\n                    ]\n                }\n            ],\n            /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://v3.tailwindcss.com/docs/placeholder-color\n       */ \"placeholder-color\": [\n                {\n                    placeholder: scaleColor()\n                }\n            ],\n            /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */ \"text-color\": [\n                {\n                    text: scaleColor()\n                }\n            ],\n            /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */ \"text-decoration\": [\n                \"underline\",\n                \"overline\",\n                \"line-through\",\n                \"no-underline\"\n            ],\n            /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */ \"text-decoration-style\": [\n                {\n                    decoration: [\n                        ...scaleLineStyle(),\n                        \"wavy\"\n                    ]\n                }\n            ],\n            /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */ \"text-decoration-thickness\": [\n                {\n                    decoration: [\n                        isNumber,\n                        \"from-font\",\n                        \"auto\",\n                        isArbitraryVariable,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */ \"text-decoration-color\": [\n                {\n                    decoration: scaleColor()\n                }\n            ],\n            /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */ \"underline-offset\": [\n                {\n                    \"underline-offset\": [\n                        isNumber,\n                        \"auto\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */ \"text-transform\": [\n                \"uppercase\",\n                \"lowercase\",\n                \"capitalize\",\n                \"normal-case\"\n            ],\n            /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */ \"text-overflow\": [\n                \"truncate\",\n                \"text-ellipsis\",\n                \"text-clip\"\n            ],\n            /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */ \"text-wrap\": [\n                {\n                    text: [\n                        \"wrap\",\n                        \"nowrap\",\n                        \"balance\",\n                        \"pretty\"\n                    ]\n                }\n            ],\n            /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */ indent: [\n                {\n                    indent: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */ \"vertical-align\": [\n                {\n                    align: [\n                        \"baseline\",\n                        \"top\",\n                        \"middle\",\n                        \"bottom\",\n                        \"text-top\",\n                        \"text-bottom\",\n                        \"sub\",\n                        \"super\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */ whitespace: [\n                {\n                    whitespace: [\n                        \"normal\",\n                        \"nowrap\",\n                        \"pre\",\n                        \"pre-line\",\n                        \"pre-wrap\",\n                        \"break-spaces\"\n                    ]\n                }\n            ],\n            /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */ break: [\n                {\n                    break: [\n                        \"normal\",\n                        \"words\",\n                        \"all\",\n                        \"keep\"\n                    ]\n                }\n            ],\n            /**\n       * Overflow Wrap\n       * @see https://tailwindcss.com/docs/overflow-wrap\n       */ wrap: [\n                {\n                    wrap: [\n                        \"break-word\",\n                        \"anywhere\",\n                        \"normal\"\n                    ]\n                }\n            ],\n            /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */ hyphens: [\n                {\n                    hyphens: [\n                        \"none\",\n                        \"manual\",\n                        \"auto\"\n                    ]\n                }\n            ],\n            /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */ content: [\n                {\n                    content: [\n                        \"none\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // -------------------\n            // --- Backgrounds ---\n            // -------------------\n            /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */ \"bg-attachment\": [\n                {\n                    bg: [\n                        \"fixed\",\n                        \"local\",\n                        \"scroll\"\n                    ]\n                }\n            ],\n            /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */ \"bg-clip\": [\n                {\n                    \"bg-clip\": [\n                        \"border\",\n                        \"padding\",\n                        \"content\",\n                        \"text\"\n                    ]\n                }\n            ],\n            /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */ \"bg-origin\": [\n                {\n                    \"bg-origin\": [\n                        \"border\",\n                        \"padding\",\n                        \"content\"\n                    ]\n                }\n            ],\n            /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */ \"bg-position\": [\n                {\n                    bg: scaleBgPosition()\n                }\n            ],\n            /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */ \"bg-repeat\": [\n                {\n                    bg: scaleBgRepeat()\n                }\n            ],\n            /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */ \"bg-size\": [\n                {\n                    bg: scaleBgSize()\n                }\n            ],\n            /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */ \"bg-image\": [\n                {\n                    bg: [\n                        \"none\",\n                        {\n                            linear: [\n                                {\n                                    to: [\n                                        \"t\",\n                                        \"tr\",\n                                        \"r\",\n                                        \"br\",\n                                        \"b\",\n                                        \"bl\",\n                                        \"l\",\n                                        \"tl\"\n                                    ]\n                                },\n                                isInteger,\n                                isArbitraryVariable,\n                                isArbitraryValue\n                            ],\n                            radial: [\n                                \"\",\n                                isArbitraryVariable,\n                                isArbitraryValue\n                            ],\n                            conic: [\n                                isInteger,\n                                isArbitraryVariable,\n                                isArbitraryValue\n                            ]\n                        },\n                        isArbitraryVariableImage,\n                        isArbitraryImage\n                    ]\n                }\n            ],\n            /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */ \"bg-color\": [\n                {\n                    bg: scaleColor()\n                }\n            ],\n            /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-from-pos\": [\n                {\n                    from: scaleGradientStopPosition()\n                }\n            ],\n            /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-via-pos\": [\n                {\n                    via: scaleGradientStopPosition()\n                }\n            ],\n            /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-to-pos\": [\n                {\n                    to: scaleGradientStopPosition()\n                }\n            ],\n            /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-from\": [\n                {\n                    from: scaleColor()\n                }\n            ],\n            /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-via\": [\n                {\n                    via: scaleColor()\n                }\n            ],\n            /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-to\": [\n                {\n                    to: scaleColor()\n                }\n            ],\n            // ---------------\n            // --- Borders ---\n            // ---------------\n            /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */ rounded: [\n                {\n                    rounded: scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-s\": [\n                {\n                    \"rounded-s\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-e\": [\n                {\n                    \"rounded-e\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-t\": [\n                {\n                    \"rounded-t\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-r\": [\n                {\n                    \"rounded-r\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-b\": [\n                {\n                    \"rounded-b\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-l\": [\n                {\n                    \"rounded-l\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-ss\": [\n                {\n                    \"rounded-ss\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-se\": [\n                {\n                    \"rounded-se\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-ee\": [\n                {\n                    \"rounded-ee\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-es\": [\n                {\n                    \"rounded-es\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-tl\": [\n                {\n                    \"rounded-tl\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-tr\": [\n                {\n                    \"rounded-tr\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-br\": [\n                {\n                    \"rounded-br\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-bl\": [\n                {\n                    \"rounded-bl\": scaleRadius()\n                }\n            ],\n            /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w\": [\n                {\n                    border: scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-x\": [\n                {\n                    \"border-x\": scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-y\": [\n                {\n                    \"border-y\": scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-s\": [\n                {\n                    \"border-s\": scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-e\": [\n                {\n                    \"border-e\": scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-t\": [\n                {\n                    \"border-t\": scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-r\": [\n                {\n                    \"border-r\": scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-b\": [\n                {\n                    \"border-b\": scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-l\": [\n                {\n                    \"border-l\": scaleBorderWidth()\n                }\n            ],\n            /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ \"divide-x\": [\n                {\n                    \"divide-x\": scaleBorderWidth()\n                }\n            ],\n            /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ \"divide-x-reverse\": [\n                \"divide-x-reverse\"\n            ],\n            /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ \"divide-y\": [\n                {\n                    \"divide-y\": scaleBorderWidth()\n                }\n            ],\n            /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ \"divide-y-reverse\": [\n                \"divide-y-reverse\"\n            ],\n            /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */ \"border-style\": [\n                {\n                    border: [\n                        ...scaleLineStyle(),\n                        \"hidden\",\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n       */ \"divide-style\": [\n                {\n                    divide: [\n                        ...scaleLineStyle(),\n                        \"hidden\",\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color\": [\n                {\n                    border: scaleColor()\n                }\n            ],\n            /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-x\": [\n                {\n                    \"border-x\": scaleColor()\n                }\n            ],\n            /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-y\": [\n                {\n                    \"border-y\": scaleColor()\n                }\n            ],\n            /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-s\": [\n                {\n                    \"border-s\": scaleColor()\n                }\n            ],\n            /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-e\": [\n                {\n                    \"border-e\": scaleColor()\n                }\n            ],\n            /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-t\": [\n                {\n                    \"border-t\": scaleColor()\n                }\n            ],\n            /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-r\": [\n                {\n                    \"border-r\": scaleColor()\n                }\n            ],\n            /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-b\": [\n                {\n                    \"border-b\": scaleColor()\n                }\n            ],\n            /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-l\": [\n                {\n                    \"border-l\": scaleColor()\n                }\n            ],\n            /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */ \"divide-color\": [\n                {\n                    divide: scaleColor()\n                }\n            ],\n            /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */ \"outline-style\": [\n                {\n                    outline: [\n                        ...scaleLineStyle(),\n                        \"none\",\n                        \"hidden\"\n                    ]\n                }\n            ],\n            /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */ \"outline-offset\": [\n                {\n                    \"outline-offset\": [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */ \"outline-w\": [\n                {\n                    outline: [\n                        \"\",\n                        isNumber,\n                        isArbitraryVariableLength,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */ \"outline-color\": [\n                {\n                    outline: scaleColor()\n                }\n            ],\n            // ---------------\n            // --- Effects ---\n            // ---------------\n            /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */ shadow: [\n                {\n                    shadow: [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        \"\",\n                        \"none\",\n                        themeShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n       */ \"shadow-color\": [\n                {\n                    shadow: scaleColor()\n                }\n            ],\n            /**\n       * Inset Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n       */ \"inset-shadow\": [\n                {\n                    \"inset-shadow\": [\n                        \"none\",\n                        themeInsetShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Inset Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n       */ \"inset-shadow-color\": [\n                {\n                    \"inset-shadow\": scaleColor()\n                }\n            ],\n            /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n       */ \"ring-w\": [\n                {\n                    ring: scaleBorderWidth()\n                }\n            ],\n            /**\n       * Ring Width Inset\n       * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */ \"ring-w-inset\": [\n                \"ring-inset\"\n            ],\n            /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n       */ \"ring-color\": [\n                {\n                    ring: scaleColor()\n                }\n            ],\n            /**\n       * Ring Offset Width\n       * @see https://v3.tailwindcss.com/docs/ring-offset-width\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */ \"ring-offset-w\": [\n                {\n                    \"ring-offset\": [\n                        isNumber,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Ring Offset Color\n       * @see https://v3.tailwindcss.com/docs/ring-offset-color\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */ \"ring-offset-color\": [\n                {\n                    \"ring-offset\": scaleColor()\n                }\n            ],\n            /**\n       * Inset Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n       */ \"inset-ring-w\": [\n                {\n                    \"inset-ring\": scaleBorderWidth()\n                }\n            ],\n            /**\n       * Inset Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n       */ \"inset-ring-color\": [\n                {\n                    \"inset-ring\": scaleColor()\n                }\n            ],\n            /**\n       * Text Shadow\n       * @see https://tailwindcss.com/docs/text-shadow\n       */ \"text-shadow\": [\n                {\n                    \"text-shadow\": [\n                        \"none\",\n                        themeTextShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Text Shadow Color\n       * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n       */ \"text-shadow-color\": [\n                {\n                    \"text-shadow\": scaleColor()\n                }\n            ],\n            /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */ opacity: [\n                {\n                    opacity: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */ \"mix-blend\": [\n                {\n                    \"mix-blend\": [\n                        ...scaleBlendMode(),\n                        \"plus-darker\",\n                        \"plus-lighter\"\n                    ]\n                }\n            ],\n            /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */ \"bg-blend\": [\n                {\n                    \"bg-blend\": scaleBlendMode()\n                }\n            ],\n            /**\n       * Mask Clip\n       * @see https://tailwindcss.com/docs/mask-clip\n       */ \"mask-clip\": [\n                {\n                    \"mask-clip\": [\n                        \"border\",\n                        \"padding\",\n                        \"content\",\n                        \"fill\",\n                        \"stroke\",\n                        \"view\"\n                    ]\n                },\n                \"mask-no-clip\"\n            ],\n            /**\n       * Mask Composite\n       * @see https://tailwindcss.com/docs/mask-composite\n       */ \"mask-composite\": [\n                {\n                    mask: [\n                        \"add\",\n                        \"subtract\",\n                        \"intersect\",\n                        \"exclude\"\n                    ]\n                }\n            ],\n            /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */ \"mask-image-linear-pos\": [\n                {\n                    \"mask-linear\": [\n                        isNumber\n                    ]\n                }\n            ],\n            \"mask-image-linear-from-pos\": [\n                {\n                    \"mask-linear-from\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-linear-to-pos\": [\n                {\n                    \"mask-linear-to\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-linear-from-color\": [\n                {\n                    \"mask-linear-from\": scaleColor()\n                }\n            ],\n            \"mask-image-linear-to-color\": [\n                {\n                    \"mask-linear-to\": scaleColor()\n                }\n            ],\n            \"mask-image-t-from-pos\": [\n                {\n                    \"mask-t-from\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-t-to-pos\": [\n                {\n                    \"mask-t-to\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-t-from-color\": [\n                {\n                    \"mask-t-from\": scaleColor()\n                }\n            ],\n            \"mask-image-t-to-color\": [\n                {\n                    \"mask-t-to\": scaleColor()\n                }\n            ],\n            \"mask-image-r-from-pos\": [\n                {\n                    \"mask-r-from\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-r-to-pos\": [\n                {\n                    \"mask-r-to\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-r-from-color\": [\n                {\n                    \"mask-r-from\": scaleColor()\n                }\n            ],\n            \"mask-image-r-to-color\": [\n                {\n                    \"mask-r-to\": scaleColor()\n                }\n            ],\n            \"mask-image-b-from-pos\": [\n                {\n                    \"mask-b-from\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-b-to-pos\": [\n                {\n                    \"mask-b-to\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-b-from-color\": [\n                {\n                    \"mask-b-from\": scaleColor()\n                }\n            ],\n            \"mask-image-b-to-color\": [\n                {\n                    \"mask-b-to\": scaleColor()\n                }\n            ],\n            \"mask-image-l-from-pos\": [\n                {\n                    \"mask-l-from\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-l-to-pos\": [\n                {\n                    \"mask-l-to\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-l-from-color\": [\n                {\n                    \"mask-l-from\": scaleColor()\n                }\n            ],\n            \"mask-image-l-to-color\": [\n                {\n                    \"mask-l-to\": scaleColor()\n                }\n            ],\n            \"mask-image-x-from-pos\": [\n                {\n                    \"mask-x-from\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-x-to-pos\": [\n                {\n                    \"mask-x-to\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-x-from-color\": [\n                {\n                    \"mask-x-from\": scaleColor()\n                }\n            ],\n            \"mask-image-x-to-color\": [\n                {\n                    \"mask-x-to\": scaleColor()\n                }\n            ],\n            \"mask-image-y-from-pos\": [\n                {\n                    \"mask-y-from\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-y-to-pos\": [\n                {\n                    \"mask-y-to\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-y-from-color\": [\n                {\n                    \"mask-y-from\": scaleColor()\n                }\n            ],\n            \"mask-image-y-to-color\": [\n                {\n                    \"mask-y-to\": scaleColor()\n                }\n            ],\n            \"mask-image-radial\": [\n                {\n                    \"mask-radial\": [\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            \"mask-image-radial-from-pos\": [\n                {\n                    \"mask-radial-from\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-radial-to-pos\": [\n                {\n                    \"mask-radial-to\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-radial-from-color\": [\n                {\n                    \"mask-radial-from\": scaleColor()\n                }\n            ],\n            \"mask-image-radial-to-color\": [\n                {\n                    \"mask-radial-to\": scaleColor()\n                }\n            ],\n            \"mask-image-radial-shape\": [\n                {\n                    \"mask-radial\": [\n                        \"circle\",\n                        \"ellipse\"\n                    ]\n                }\n            ],\n            \"mask-image-radial-size\": [\n                {\n                    \"mask-radial\": [\n                        {\n                            closest: [\n                                \"side\",\n                                \"corner\"\n                            ],\n                            farthest: [\n                                \"side\",\n                                \"corner\"\n                            ]\n                        }\n                    ]\n                }\n            ],\n            \"mask-image-radial-pos\": [\n                {\n                    \"mask-radial-at\": scalePosition()\n                }\n            ],\n            \"mask-image-conic-pos\": [\n                {\n                    \"mask-conic\": [\n                        isNumber\n                    ]\n                }\n            ],\n            \"mask-image-conic-from-pos\": [\n                {\n                    \"mask-conic-from\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-conic-to-pos\": [\n                {\n                    \"mask-conic-to\": scaleMaskImagePosition()\n                }\n            ],\n            \"mask-image-conic-from-color\": [\n                {\n                    \"mask-conic-from\": scaleColor()\n                }\n            ],\n            \"mask-image-conic-to-color\": [\n                {\n                    \"mask-conic-to\": scaleColor()\n                }\n            ],\n            /**\n       * Mask Mode\n       * @see https://tailwindcss.com/docs/mask-mode\n       */ \"mask-mode\": [\n                {\n                    mask: [\n                        \"alpha\",\n                        \"luminance\",\n                        \"match\"\n                    ]\n                }\n            ],\n            /**\n       * Mask Origin\n       * @see https://tailwindcss.com/docs/mask-origin\n       */ \"mask-origin\": [\n                {\n                    \"mask-origin\": [\n                        \"border\",\n                        \"padding\",\n                        \"content\",\n                        \"fill\",\n                        \"stroke\",\n                        \"view\"\n                    ]\n                }\n            ],\n            /**\n       * Mask Position\n       * @see https://tailwindcss.com/docs/mask-position\n       */ \"mask-position\": [\n                {\n                    mask: scaleBgPosition()\n                }\n            ],\n            /**\n       * Mask Repeat\n       * @see https://tailwindcss.com/docs/mask-repeat\n       */ \"mask-repeat\": [\n                {\n                    mask: scaleBgRepeat()\n                }\n            ],\n            /**\n       * Mask Size\n       * @see https://tailwindcss.com/docs/mask-size\n       */ \"mask-size\": [\n                {\n                    mask: scaleBgSize()\n                }\n            ],\n            /**\n       * Mask Type\n       * @see https://tailwindcss.com/docs/mask-type\n       */ \"mask-type\": [\n                {\n                    \"mask-type\": [\n                        \"alpha\",\n                        \"luminance\"\n                    ]\n                }\n            ],\n            /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */ \"mask-image\": [\n                {\n                    mask: [\n                        \"none\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // ---------------\n            // --- Filters ---\n            // ---------------\n            /**\n       * Filter\n       * @see https://tailwindcss.com/docs/filter\n       */ filter: [\n                {\n                    filter: [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        \"\",\n                        \"none\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */ blur: [\n                {\n                    blur: scaleBlur()\n                }\n            ],\n            /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */ brightness: [\n                {\n                    brightness: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */ contrast: [\n                {\n                    contrast: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */ \"drop-shadow\": [\n                {\n                    \"drop-shadow\": [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        \"\",\n                        \"none\",\n                        themeDropShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Drop Shadow Color\n       * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n       */ \"drop-shadow-color\": [\n                {\n                    \"drop-shadow\": scaleColor()\n                }\n            ],\n            /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */ grayscale: [\n                {\n                    grayscale: [\n                        \"\",\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */ \"hue-rotate\": [\n                {\n                    \"hue-rotate\": [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */ invert: [\n                {\n                    invert: [\n                        \"\",\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */ saturate: [\n                {\n                    saturate: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */ sepia: [\n                {\n                    sepia: [\n                        \"\",\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Filter\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */ \"backdrop-filter\": [\n                {\n                    \"backdrop-filter\": [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        \"\",\n                        \"none\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */ \"backdrop-blur\": [\n                {\n                    \"backdrop-blur\": scaleBlur()\n                }\n            ],\n            /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */ \"backdrop-brightness\": [\n                {\n                    \"backdrop-brightness\": [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */ \"backdrop-contrast\": [\n                {\n                    \"backdrop-contrast\": [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */ \"backdrop-grayscale\": [\n                {\n                    \"backdrop-grayscale\": [\n                        \"\",\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */ \"backdrop-hue-rotate\": [\n                {\n                    \"backdrop-hue-rotate\": [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */ \"backdrop-invert\": [\n                {\n                    \"backdrop-invert\": [\n                        \"\",\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */ \"backdrop-opacity\": [\n                {\n                    \"backdrop-opacity\": [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */ \"backdrop-saturate\": [\n                {\n                    \"backdrop-saturate\": [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */ \"backdrop-sepia\": [\n                {\n                    \"backdrop-sepia\": [\n                        \"\",\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // --------------\n            // --- Tables ---\n            // --------------\n            /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */ \"border-collapse\": [\n                {\n                    border: [\n                        \"collapse\",\n                        \"separate\"\n                    ]\n                }\n            ],\n            /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ \"border-spacing\": [\n                {\n                    \"border-spacing\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ \"border-spacing-x\": [\n                {\n                    \"border-spacing-x\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ \"border-spacing-y\": [\n                {\n                    \"border-spacing-y\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */ \"table-layout\": [\n                {\n                    table: [\n                        \"auto\",\n                        \"fixed\"\n                    ]\n                }\n            ],\n            /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */ caption: [\n                {\n                    caption: [\n                        \"top\",\n                        \"bottom\"\n                    ]\n                }\n            ],\n            // ---------------------------------\n            // --- Transitions and Animation ---\n            // ---------------------------------\n            /**\n       * Transition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */ transition: [\n                {\n                    transition: [\n                        \"\",\n                        \"all\",\n                        \"colors\",\n                        \"opacity\",\n                        \"shadow\",\n                        \"transform\",\n                        \"none\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Transition Behavior\n       * @see https://tailwindcss.com/docs/transition-behavior\n       */ \"transition-behavior\": [\n                {\n                    transition: [\n                        \"normal\",\n                        \"discrete\"\n                    ]\n                }\n            ],\n            /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */ duration: [\n                {\n                    duration: [\n                        isNumber,\n                        \"initial\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */ ease: [\n                {\n                    ease: [\n                        \"linear\",\n                        \"initial\",\n                        themeEase,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */ delay: [\n                {\n                    delay: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */ animate: [\n                {\n                    animate: [\n                        \"none\",\n                        themeAnimate,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // ------------------\n            // --- Transforms ---\n            // ------------------\n            /**\n       * Backface Visibility\n       * @see https://tailwindcss.com/docs/backface-visibility\n       */ backface: [\n                {\n                    backface: [\n                        \"hidden\",\n                        \"visible\"\n                    ]\n                }\n            ],\n            /**\n       * Perspective\n       * @see https://tailwindcss.com/docs/perspective\n       */ perspective: [\n                {\n                    perspective: [\n                        themePerspective,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Perspective Origin\n       * @see https://tailwindcss.com/docs/perspective-origin\n       */ \"perspective-origin\": [\n                {\n                    \"perspective-origin\": scalePositionWithArbitrary()\n                }\n            ],\n            /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */ rotate: [\n                {\n                    rotate: scaleRotate()\n                }\n            ],\n            /**\n       * Rotate X\n       * @see https://tailwindcss.com/docs/rotate\n       */ \"rotate-x\": [\n                {\n                    \"rotate-x\": scaleRotate()\n                }\n            ],\n            /**\n       * Rotate Y\n       * @see https://tailwindcss.com/docs/rotate\n       */ \"rotate-y\": [\n                {\n                    \"rotate-y\": scaleRotate()\n                }\n            ],\n            /**\n       * Rotate Z\n       * @see https://tailwindcss.com/docs/rotate\n       */ \"rotate-z\": [\n                {\n                    \"rotate-z\": scaleRotate()\n                }\n            ],\n            /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */ scale: [\n                {\n                    scale: scaleScale()\n                }\n            ],\n            /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */ \"scale-x\": [\n                {\n                    \"scale-x\": scaleScale()\n                }\n            ],\n            /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */ \"scale-y\": [\n                {\n                    \"scale-y\": scaleScale()\n                }\n            ],\n            /**\n       * Scale Z\n       * @see https://tailwindcss.com/docs/scale\n       */ \"scale-z\": [\n                {\n                    \"scale-z\": scaleScale()\n                }\n            ],\n            /**\n       * Scale 3D\n       * @see https://tailwindcss.com/docs/scale\n       */ \"scale-3d\": [\n                \"scale-3d\"\n            ],\n            /**\n       * Skew\n       * @see https://tailwindcss.com/docs/skew\n       */ skew: [\n                {\n                    skew: scaleSkew()\n                }\n            ],\n            /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */ \"skew-x\": [\n                {\n                    \"skew-x\": scaleSkew()\n                }\n            ],\n            /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */ \"skew-y\": [\n                {\n                    \"skew-y\": scaleSkew()\n                }\n            ],\n            /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */ transform: [\n                {\n                    transform: [\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                        \"\",\n                        \"none\",\n                        \"gpu\",\n                        \"cpu\"\n                    ]\n                }\n            ],\n            /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */ \"transform-origin\": [\n                {\n                    origin: scalePositionWithArbitrary()\n                }\n            ],\n            /**\n       * Transform Style\n       * @see https://tailwindcss.com/docs/transform-style\n       */ \"transform-style\": [\n                {\n                    transform: [\n                        \"3d\",\n                        \"flat\"\n                    ]\n                }\n            ],\n            /**\n       * Translate\n       * @see https://tailwindcss.com/docs/translate\n       */ translate: [\n                {\n                    translate: scaleTranslate()\n                }\n            ],\n            /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */ \"translate-x\": [\n                {\n                    \"translate-x\": scaleTranslate()\n                }\n            ],\n            /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */ \"translate-y\": [\n                {\n                    \"translate-y\": scaleTranslate()\n                }\n            ],\n            /**\n       * Translate Z\n       * @see https://tailwindcss.com/docs/translate\n       */ \"translate-z\": [\n                {\n                    \"translate-z\": scaleTranslate()\n                }\n            ],\n            /**\n       * Translate None\n       * @see https://tailwindcss.com/docs/translate\n       */ \"translate-none\": [\n                \"translate-none\"\n            ],\n            // ---------------------\n            // --- Interactivity ---\n            // ---------------------\n            /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */ accent: [\n                {\n                    accent: scaleColor()\n                }\n            ],\n            /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */ appearance: [\n                {\n                    appearance: [\n                        \"none\",\n                        \"auto\"\n                    ]\n                }\n            ],\n            /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */ \"caret-color\": [\n                {\n                    caret: scaleColor()\n                }\n            ],\n            /**\n       * Color Scheme\n       * @see https://tailwindcss.com/docs/color-scheme\n       */ \"color-scheme\": [\n                {\n                    scheme: [\n                        \"normal\",\n                        \"dark\",\n                        \"light\",\n                        \"light-dark\",\n                        \"only-dark\",\n                        \"only-light\"\n                    ]\n                }\n            ],\n            /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */ cursor: [\n                {\n                    cursor: [\n                        \"auto\",\n                        \"default\",\n                        \"pointer\",\n                        \"wait\",\n                        \"text\",\n                        \"move\",\n                        \"help\",\n                        \"not-allowed\",\n                        \"none\",\n                        \"context-menu\",\n                        \"progress\",\n                        \"cell\",\n                        \"crosshair\",\n                        \"vertical-text\",\n                        \"alias\",\n                        \"copy\",\n                        \"no-drop\",\n                        \"grab\",\n                        \"grabbing\",\n                        \"all-scroll\",\n                        \"col-resize\",\n                        \"row-resize\",\n                        \"n-resize\",\n                        \"e-resize\",\n                        \"s-resize\",\n                        \"w-resize\",\n                        \"ne-resize\",\n                        \"nw-resize\",\n                        \"se-resize\",\n                        \"sw-resize\",\n                        \"ew-resize\",\n                        \"ns-resize\",\n                        \"nesw-resize\",\n                        \"nwse-resize\",\n                        \"zoom-in\",\n                        \"zoom-out\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Field Sizing\n       * @see https://tailwindcss.com/docs/field-sizing\n       */ \"field-sizing\": [\n                {\n                    \"field-sizing\": [\n                        \"fixed\",\n                        \"content\"\n                    ]\n                }\n            ],\n            /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */ \"pointer-events\": [\n                {\n                    \"pointer-events\": [\n                        \"auto\",\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */ resize: [\n                {\n                    resize: [\n                        \"none\",\n                        \"\",\n                        \"y\",\n                        \"x\"\n                    ]\n                }\n            ],\n            /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */ \"scroll-behavior\": [\n                {\n                    scroll: [\n                        \"auto\",\n                        \"smooth\"\n                    ]\n                }\n            ],\n            /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-m\": [\n                {\n                    \"scroll-m\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-mx\": [\n                {\n                    \"scroll-mx\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-my\": [\n                {\n                    \"scroll-my\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-ms\": [\n                {\n                    \"scroll-ms\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-me\": [\n                {\n                    \"scroll-me\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-mt\": [\n                {\n                    \"scroll-mt\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-mr\": [\n                {\n                    \"scroll-mr\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-mb\": [\n                {\n                    \"scroll-mb\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-ml\": [\n                {\n                    \"scroll-ml\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-p\": [\n                {\n                    \"scroll-p\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-px\": [\n                {\n                    \"scroll-px\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-py\": [\n                {\n                    \"scroll-py\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-ps\": [\n                {\n                    \"scroll-ps\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-pe\": [\n                {\n                    \"scroll-pe\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-pt\": [\n                {\n                    \"scroll-pt\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-pr\": [\n                {\n                    \"scroll-pr\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-pb\": [\n                {\n                    \"scroll-pb\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-pl\": [\n                {\n                    \"scroll-pl\": scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */ \"snap-align\": [\n                {\n                    snap: [\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"align-none\"\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */ \"snap-stop\": [\n                {\n                    snap: [\n                        \"normal\",\n                        \"always\"\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */ \"snap-type\": [\n                {\n                    snap: [\n                        \"none\",\n                        \"x\",\n                        \"y\",\n                        \"both\"\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */ \"snap-strictness\": [\n                {\n                    snap: [\n                        \"mandatory\",\n                        \"proximity\"\n                    ]\n                }\n            ],\n            /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */ touch: [\n                {\n                    touch: [\n                        \"auto\",\n                        \"none\",\n                        \"manipulation\"\n                    ]\n                }\n            ],\n            /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */ \"touch-x\": [\n                {\n                    \"touch-pan\": [\n                        \"x\",\n                        \"left\",\n                        \"right\"\n                    ]\n                }\n            ],\n            /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */ \"touch-y\": [\n                {\n                    \"touch-pan\": [\n                        \"y\",\n                        \"up\",\n                        \"down\"\n                    ]\n                }\n            ],\n            /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */ \"touch-pz\": [\n                \"touch-pinch-zoom\"\n            ],\n            /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */ select: [\n                {\n                    select: [\n                        \"none\",\n                        \"text\",\n                        \"all\",\n                        \"auto\"\n                    ]\n                }\n            ],\n            /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */ \"will-change\": [\n                {\n                    \"will-change\": [\n                        \"auto\",\n                        \"scroll\",\n                        \"contents\",\n                        \"transform\",\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // -----------\n            // --- SVG ---\n            // -----------\n            /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */ fill: [\n                {\n                    fill: [\n                        \"none\",\n                        ...scaleColor()\n                    ]\n                }\n            ],\n            /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */ \"stroke-w\": [\n                {\n                    stroke: [\n                        isNumber,\n                        isArbitraryVariableLength,\n                        isArbitraryLength,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */ stroke: [\n                {\n                    stroke: [\n                        \"none\",\n                        ...scaleColor()\n                    ]\n                }\n            ],\n            // ---------------------\n            // --- Accessibility ---\n            // ---------------------\n            /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */ \"forced-color-adjust\": [\n                {\n                    \"forced-color-adjust\": [\n                        \"auto\",\n                        \"none\"\n                    ]\n                }\n            ]\n        },\n        conflictingClassGroups: {\n            overflow: [\n                \"overflow-x\",\n                \"overflow-y\"\n            ],\n            overscroll: [\n                \"overscroll-x\",\n                \"overscroll-y\"\n            ],\n            inset: [\n                \"inset-x\",\n                \"inset-y\",\n                \"start\",\n                \"end\",\n                \"top\",\n                \"right\",\n                \"bottom\",\n                \"left\"\n            ],\n            \"inset-x\": [\n                \"right\",\n                \"left\"\n            ],\n            \"inset-y\": [\n                \"top\",\n                \"bottom\"\n            ],\n            flex: [\n                \"basis\",\n                \"grow\",\n                \"shrink\"\n            ],\n            gap: [\n                \"gap-x\",\n                \"gap-y\"\n            ],\n            p: [\n                \"px\",\n                \"py\",\n                \"ps\",\n                \"pe\",\n                \"pt\",\n                \"pr\",\n                \"pb\",\n                \"pl\"\n            ],\n            px: [\n                \"pr\",\n                \"pl\"\n            ],\n            py: [\n                \"pt\",\n                \"pb\"\n            ],\n            m: [\n                \"mx\",\n                \"my\",\n                \"ms\",\n                \"me\",\n                \"mt\",\n                \"mr\",\n                \"mb\",\n                \"ml\"\n            ],\n            mx: [\n                \"mr\",\n                \"ml\"\n            ],\n            my: [\n                \"mt\",\n                \"mb\"\n            ],\n            size: [\n                \"w\",\n                \"h\"\n            ],\n            \"font-size\": [\n                \"leading\"\n            ],\n            \"fvn-normal\": [\n                \"fvn-ordinal\",\n                \"fvn-slashed-zero\",\n                \"fvn-figure\",\n                \"fvn-spacing\",\n                \"fvn-fraction\"\n            ],\n            \"fvn-ordinal\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-slashed-zero\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-figure\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-spacing\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-fraction\": [\n                \"fvn-normal\"\n            ],\n            \"line-clamp\": [\n                \"display\",\n                \"overflow\"\n            ],\n            rounded: [\n                \"rounded-s\",\n                \"rounded-e\",\n                \"rounded-t\",\n                \"rounded-r\",\n                \"rounded-b\",\n                \"rounded-l\",\n                \"rounded-ss\",\n                \"rounded-se\",\n                \"rounded-ee\",\n                \"rounded-es\",\n                \"rounded-tl\",\n                \"rounded-tr\",\n                \"rounded-br\",\n                \"rounded-bl\"\n            ],\n            \"rounded-s\": [\n                \"rounded-ss\",\n                \"rounded-es\"\n            ],\n            \"rounded-e\": [\n                \"rounded-se\",\n                \"rounded-ee\"\n            ],\n            \"rounded-t\": [\n                \"rounded-tl\",\n                \"rounded-tr\"\n            ],\n            \"rounded-r\": [\n                \"rounded-tr\",\n                \"rounded-br\"\n            ],\n            \"rounded-b\": [\n                \"rounded-br\",\n                \"rounded-bl\"\n            ],\n            \"rounded-l\": [\n                \"rounded-tl\",\n                \"rounded-bl\"\n            ],\n            \"border-spacing\": [\n                \"border-spacing-x\",\n                \"border-spacing-y\"\n            ],\n            \"border-w\": [\n                \"border-w-x\",\n                \"border-w-y\",\n                \"border-w-s\",\n                \"border-w-e\",\n                \"border-w-t\",\n                \"border-w-r\",\n                \"border-w-b\",\n                \"border-w-l\"\n            ],\n            \"border-w-x\": [\n                \"border-w-r\",\n                \"border-w-l\"\n            ],\n            \"border-w-y\": [\n                \"border-w-t\",\n                \"border-w-b\"\n            ],\n            \"border-color\": [\n                \"border-color-x\",\n                \"border-color-y\",\n                \"border-color-s\",\n                \"border-color-e\",\n                \"border-color-t\",\n                \"border-color-r\",\n                \"border-color-b\",\n                \"border-color-l\"\n            ],\n            \"border-color-x\": [\n                \"border-color-r\",\n                \"border-color-l\"\n            ],\n            \"border-color-y\": [\n                \"border-color-t\",\n                \"border-color-b\"\n            ],\n            translate: [\n                \"translate-x\",\n                \"translate-y\",\n                \"translate-none\"\n            ],\n            \"translate-none\": [\n                \"translate\",\n                \"translate-x\",\n                \"translate-y\",\n                \"translate-z\"\n            ],\n            \"scroll-m\": [\n                \"scroll-mx\",\n                \"scroll-my\",\n                \"scroll-ms\",\n                \"scroll-me\",\n                \"scroll-mt\",\n                \"scroll-mr\",\n                \"scroll-mb\",\n                \"scroll-ml\"\n            ],\n            \"scroll-mx\": [\n                \"scroll-mr\",\n                \"scroll-ml\"\n            ],\n            \"scroll-my\": [\n                \"scroll-mt\",\n                \"scroll-mb\"\n            ],\n            \"scroll-p\": [\n                \"scroll-px\",\n                \"scroll-py\",\n                \"scroll-ps\",\n                \"scroll-pe\",\n                \"scroll-pt\",\n                \"scroll-pr\",\n                \"scroll-pb\",\n                \"scroll-pl\"\n            ],\n            \"scroll-px\": [\n                \"scroll-pr\",\n                \"scroll-pl\"\n            ],\n            \"scroll-py\": [\n                \"scroll-pt\",\n                \"scroll-pb\"\n            ],\n            touch: [\n                \"touch-x\",\n                \"touch-y\",\n                \"touch-pz\"\n            ],\n            \"touch-x\": [\n                \"touch\"\n            ],\n            \"touch-y\": [\n                \"touch\"\n            ],\n            \"touch-pz\": [\n                \"touch\"\n            ]\n        },\n        conflictingClassGroupModifiers: {\n            \"font-size\": [\n                \"leading\"\n            ]\n        },\n        orderSensitiveModifiers: [\n            \"*\",\n            \"**\",\n            \"after\",\n            \"backdrop\",\n            \"before\",\n            \"details-content\",\n            \"file\",\n            \"first-letter\",\n            \"first-line\",\n            \"marker\",\n            \"placeholder\",\n            \"selection\"\n        ]\n    };\n};\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */ const mergeConfigs = (baseConfig, { cacheSize, prefix, experimentalParseClassName, extend = {}, override = {} })=>{\n    overrideProperty(baseConfig, \"cacheSize\", cacheSize);\n    overrideProperty(baseConfig, \"prefix\", prefix);\n    overrideProperty(baseConfig, \"experimentalParseClassName\", experimentalParseClassName);\n    overrideConfigProperties(baseConfig.theme, override.theme);\n    overrideConfigProperties(baseConfig.classGroups, override.classGroups);\n    overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups);\n    overrideConfigProperties(baseConfig.conflictingClassGroupModifiers, override.conflictingClassGroupModifiers);\n    overrideProperty(baseConfig, \"orderSensitiveModifiers\", override.orderSensitiveModifiers);\n    mergeConfigProperties(baseConfig.theme, extend.theme);\n    mergeConfigProperties(baseConfig.classGroups, extend.classGroups);\n    mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups);\n    mergeConfigProperties(baseConfig.conflictingClassGroupModifiers, extend.conflictingClassGroupModifiers);\n    mergeArrayProperties(baseConfig, extend, \"orderSensitiveModifiers\");\n    return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue)=>{\n    if (overrideValue !== undefined) {\n        baseObject[overrideKey] = overrideValue;\n    }\n};\nconst overrideConfigProperties = (baseObject, overrideObject)=>{\n    if (overrideObject) {\n        for(const key in overrideObject){\n            overrideProperty(baseObject, key, overrideObject[key]);\n        }\n    }\n};\nconst mergeConfigProperties = (baseObject, mergeObject)=>{\n    if (mergeObject) {\n        for(const key in mergeObject){\n            mergeArrayProperties(baseObject, mergeObject, key);\n        }\n    }\n};\nconst mergeArrayProperties = (baseObject, mergeObject, key)=>{\n    const mergeValue = mergeObject[key];\n    if (mergeValue !== undefined) {\n        baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue;\n    }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig)=>typeof configExtension === \"function\" ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(()=>mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/ createTailwindMerge(getDefaultConfig);\n //# sourceMappingURL=bundle-mjs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\n");

/***/ })

};
;