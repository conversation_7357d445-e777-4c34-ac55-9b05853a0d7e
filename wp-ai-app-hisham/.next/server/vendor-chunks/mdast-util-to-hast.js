"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-hast";
exports.ids = ["vendor-chunks/mdast-util-to-hast"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js":
/*!*******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/footer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultFootnoteBackContent: () => (/* binding */ defaultFootnoteBackContent),\n/* harmony export */   defaultFootnoteBackLabel: () => (/* binding */ defaultFootnoteBackLabel),\n/* harmony export */   footer: () => (/* binding */ footer)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */ /**\n * @callback FootnoteBackContentTemplate\n *   Generate content for the backreference dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent> | ElementContent | string}\n *   Content for the backreference when linking back from definitions to their\n *   reference.\n *\n * @callback FootnoteBackLabelTemplate\n *   Generate a back label dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Back label to use when linking back from definitions to their reference.\n */ \n\n/**\n * Generate the default content that GitHub uses on backreferences.\n *\n * @param {number} _\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent>}\n *   Content.\n */ function defaultFootnoteBackContent(_, rereferenceIndex) {\n    /** @type {Array<ElementContent>} */ const result = [\n        {\n            type: \"text\",\n            value: \"↩\"\n        }\n    ];\n    if (rereferenceIndex > 1) {\n        result.push({\n            type: \"element\",\n            tagName: \"sup\",\n            properties: {},\n            children: [\n                {\n                    type: \"text\",\n                    value: String(rereferenceIndex)\n                }\n            ]\n        });\n    }\n    return result;\n}\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Label.\n */ function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n    return \"Back to reference \" + (referenceIndex + 1) + (rereferenceIndex > 1 ? \"-\" + rereferenceIndex : \"\");\n}\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */ // eslint-disable-next-line complexity\nfunction footer(state) {\n    const clobberPrefix = typeof state.options.clobberPrefix === \"string\" ? state.options.clobberPrefix : \"user-content-\";\n    const footnoteBackContent = state.options.footnoteBackContent || defaultFootnoteBackContent;\n    const footnoteBackLabel = state.options.footnoteBackLabel || defaultFootnoteBackLabel;\n    const footnoteLabel = state.options.footnoteLabel || \"Footnotes\";\n    const footnoteLabelTagName = state.options.footnoteLabelTagName || \"h2\";\n    const footnoteLabelProperties = state.options.footnoteLabelProperties || {\n        className: [\n            \"sr-only\"\n        ]\n    };\n    /** @type {Array<ElementContent>} */ const listItems = [];\n    let referenceIndex = -1;\n    while(++referenceIndex < state.footnoteOrder.length){\n        const definition = state.footnoteById.get(state.footnoteOrder[referenceIndex]);\n        if (!definition) {\n            continue;\n        }\n        const content = state.all(definition);\n        const id = String(definition.identifier).toUpperCase();\n        const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(id.toLowerCase());\n        let rereferenceIndex = 0;\n        /** @type {Array<ElementContent>} */ const backReferences = [];\n        const counts = state.footnoteCounts.get(id);\n        // eslint-disable-next-line no-unmodified-loop-condition\n        while(counts !== undefined && ++rereferenceIndex <= counts){\n            if (backReferences.length > 0) {\n                backReferences.push({\n                    type: \"text\",\n                    value: \" \"\n                });\n            }\n            let children = typeof footnoteBackContent === \"string\" ? footnoteBackContent : footnoteBackContent(referenceIndex, rereferenceIndex);\n            if (typeof children === \"string\") {\n                children = {\n                    type: \"text\",\n                    value: children\n                };\n            }\n            backReferences.push({\n                type: \"element\",\n                tagName: \"a\",\n                properties: {\n                    href: \"#\" + clobberPrefix + \"fnref-\" + safeId + (rereferenceIndex > 1 ? \"-\" + rereferenceIndex : \"\"),\n                    dataFootnoteBackref: \"\",\n                    ariaLabel: typeof footnoteBackLabel === \"string\" ? footnoteBackLabel : footnoteBackLabel(referenceIndex, rereferenceIndex),\n                    className: [\n                        \"data-footnote-backref\"\n                    ]\n                },\n                children: Array.isArray(children) ? children : [\n                    children\n                ]\n            });\n        }\n        const tail = content[content.length - 1];\n        if (tail && tail.type === \"element\" && tail.tagName === \"p\") {\n            const tailTail = tail.children[tail.children.length - 1];\n            if (tailTail && tailTail.type === \"text\") {\n                tailTail.value += \" \";\n            } else {\n                tail.children.push({\n                    type: \"text\",\n                    value: \" \"\n                });\n            }\n            tail.children.push(...backReferences);\n        } else {\n            content.push(...backReferences);\n        }\n        /** @type {Element} */ const listItem = {\n            type: \"element\",\n            tagName: \"li\",\n            properties: {\n                id: clobberPrefix + \"fn-\" + safeId\n            },\n            children: state.wrap(content, true)\n        };\n        state.patch(definition, listItem);\n        listItems.push(listItem);\n    }\n    if (listItems.length === 0) {\n        return;\n    }\n    return {\n        type: \"element\",\n        tagName: \"section\",\n        properties: {\n            dataFootnotes: true,\n            className: [\n                \"footnotes\"\n            ]\n        },\n        children: [\n            {\n                type: \"element\",\n                tagName: footnoteLabelTagName,\n                properties: {\n                    ...(0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(footnoteLabelProperties),\n                    id: \"footnote-label\"\n                },\n                children: [\n                    {\n                        type: \"text\",\n                        value: footnoteLabel\n                    }\n                ]\n            },\n            {\n                type: \"text\",\n                value: \"\\n\"\n            },\n            {\n                type: \"element\",\n                tagName: \"ol\",\n                properties: {},\n                children: state.wrap(listItems, true)\n            },\n            {\n                type: \"text\",\n                value: \"\\n\"\n            }\n        ]\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function blockquote(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"blockquote\",\n        properties: {},\n        children: state.wrap(state.all(node), true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7OztDQUlDLEdBRUQsbURBQW1EO0FBQ25EO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0EsV0FBV0MsS0FBSyxFQUFFQyxJQUFJO0lBQ3BDLG9CQUFvQixHQUNwQixNQUFNQyxTQUFTO1FBQ2JDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxZQUFZLENBQUM7UUFDYkMsVUFBVU4sTUFBTU8sSUFBSSxDQUFDUCxNQUFNUSxHQUFHLENBQUNQLE9BQU87SUFDeEM7SUFDQUQsTUFBTVMsS0FBSyxDQUFDUixNQUFNQztJQUNsQixPQUFPRixNQUFNVSxTQUFTLENBQUNULE1BQU1DO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2Jsb2NrcXVvdGUuanM/ZGE5MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkJsb2NrcXVvdGV9IEJsb2NrcXVvdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGJsb2NrcXVvdGVgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7QmxvY2txdW90ZX0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJsb2NrcXVvdGUoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdibG9ja3F1b3RlJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUud3JhcChzdGF0ZS5hbGwobm9kZSksIHRydWUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6WyJibG9ja3F1b3RlIiwic3RhdGUiLCJub2RlIiwicmVzdWx0IiwidHlwZSIsInRhZ05hbWUiLCJwcm9wZXJ0aWVzIiwiY2hpbGRyZW4iLCJ3cmFwIiwiYWxsIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/break.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */ function hardBreak(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"br\",\n        properties: {},\n        children: []\n    };\n    state.patch(node, result);\n    return [\n        state.applyData(node, result),\n        {\n            type: \"text\",\n            value: \"\\n\"\n        }\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9icmVhay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7O0NBS0MsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxVQUFVQyxLQUFLLEVBQUVDLElBQUk7SUFDbkMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFBQ0MsTUFBTTtRQUFXQyxTQUFTO1FBQU1DLFlBQVksQ0FBQztRQUFHQyxVQUFVLEVBQUU7SUFBQTtJQUM1RU4sTUFBTU8sS0FBSyxDQUFDTixNQUFNQztJQUNsQixPQUFPO1FBQUNGLE1BQU1RLFNBQVMsQ0FBQ1AsTUFBTUM7UUFBUztZQUFDQyxNQUFNO1lBQVFNLE9BQU87UUFBSTtLQUFFO0FBQ3JFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2JyZWFrLmpzPzZiOGEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLlRleHR9IFRleHRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuQnJlYWt9IEJyZWFrXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBicmVha2Agbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtCcmVha30gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0FycmF5PEVsZW1lbnQgfCBUZXh0Pn1cbiAqICAgaGFzdCBlbGVtZW50IGNvbnRlbnQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoYXJkQnJlYWsoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ2VsZW1lbnQnLCB0YWdOYW1lOiAnYnInLCBwcm9wZXJ0aWVzOiB7fSwgY2hpbGRyZW46IFtdfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBbc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdCksIHt0eXBlOiAndGV4dCcsIHZhbHVlOiAnXFxuJ31dXG59XG4iXSwibmFtZXMiOlsiaGFyZEJyZWFrIiwic3RhdGUiLCJub2RlIiwicmVzdWx0IiwidHlwZSIsInRhZ05hbWUiLCJwcm9wZXJ0aWVzIiwiY2hpbGRyZW4iLCJwYXRjaCIsImFwcGx5RGF0YSIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/code.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function code(state, node) {\n    const value = node.value ? node.value + \"\\n\" : \"\";\n    /** @type {Properties} */ const properties = {};\n    if (node.lang) {\n        properties.className = [\n            \"language-\" + node.lang\n        ];\n    }\n    // Create `<code>`.\n    /** @type {Element} */ let result = {\n        type: \"element\",\n        tagName: \"code\",\n        properties,\n        children: [\n            {\n                type: \"text\",\n                value\n            }\n        ]\n    };\n    if (node.meta) {\n        result.data = {\n            meta: node.meta\n        };\n    }\n    state.patch(node, result);\n    result = state.applyData(node, result);\n    // Create `<pre>`.\n    result = {\n        type: \"element\",\n        tagName: \"pre\",\n        properties: {},\n        children: [\n            result\n        ]\n    };\n    state.patch(node, result);\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9jb2RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNBLEtBQUtDLEtBQUssRUFBRUMsSUFBSTtJQUM5QixNQUFNQyxRQUFRRCxLQUFLQyxLQUFLLEdBQUdELEtBQUtDLEtBQUssR0FBRyxPQUFPO0lBQy9DLHVCQUF1QixHQUN2QixNQUFNQyxhQUFhLENBQUM7SUFFcEIsSUFBSUYsS0FBS0csSUFBSSxFQUFFO1FBQ2JELFdBQVdFLFNBQVMsR0FBRztZQUFDLGNBQWNKLEtBQUtHLElBQUk7U0FBQztJQUNsRDtJQUVBLG1CQUFtQjtJQUNuQixvQkFBb0IsR0FDcEIsSUFBSUUsU0FBUztRQUNYQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEw7UUFDQU0sVUFBVTtZQUFDO2dCQUFDRixNQUFNO2dCQUFRTDtZQUFLO1NBQUU7SUFDbkM7SUFFQSxJQUFJRCxLQUFLUyxJQUFJLEVBQUU7UUFDYkosT0FBT0ssSUFBSSxHQUFHO1lBQUNELE1BQU1ULEtBQUtTLElBQUk7UUFBQTtJQUNoQztJQUVBVixNQUFNWSxLQUFLLENBQUNYLE1BQU1LO0lBQ2xCQSxTQUFTTixNQUFNYSxTQUFTLENBQUNaLE1BQU1LO0lBRS9CLGtCQUFrQjtJQUNsQkEsU0FBUztRQUFDQyxNQUFNO1FBQVdDLFNBQVM7UUFBT0wsWUFBWSxDQUFDO1FBQUdNLFVBQVU7WUFBQ0g7U0FBTztJQUFBO0lBQzdFTixNQUFNWSxLQUFLLENBQUNYLE1BQU1LO0lBQ2xCLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvY29kZS5qcz8xMmZkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5Qcm9wZXJ0aWVzfSBQcm9wZXJ0aWVzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkNvZGV9IENvZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGNvZGVgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7Q29kZX0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNvZGUoc3RhdGUsIG5vZGUpIHtcbiAgY29uc3QgdmFsdWUgPSBub2RlLnZhbHVlID8gbm9kZS52YWx1ZSArICdcXG4nIDogJydcbiAgLyoqIEB0eXBlIHtQcm9wZXJ0aWVzfSAqL1xuICBjb25zdCBwcm9wZXJ0aWVzID0ge31cblxuICBpZiAobm9kZS5sYW5nKSB7XG4gICAgcHJvcGVydGllcy5jbGFzc05hbWUgPSBbJ2xhbmd1YWdlLScgKyBub2RlLmxhbmddXG4gIH1cblxuICAvLyBDcmVhdGUgYDxjb2RlPmAuXG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgbGV0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ2NvZGUnLFxuICAgIHByb3BlcnRpZXMsXG4gICAgY2hpbGRyZW46IFt7dHlwZTogJ3RleHQnLCB2YWx1ZX1dXG4gIH1cblxuICBpZiAobm9kZS5tZXRhKSB7XG4gICAgcmVzdWx0LmRhdGEgPSB7bWV0YTogbm9kZS5tZXRhfVxuICB9XG5cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXN1bHQgPSBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxuXG4gIC8vIENyZWF0ZSBgPHByZT5gLlxuICByZXN1bHQgPSB7dHlwZTogJ2VsZW1lbnQnLCB0YWdOYW1lOiAncHJlJywgcHJvcGVydGllczoge30sIGNoaWxkcmVuOiBbcmVzdWx0XX1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gcmVzdWx0XG59XG4iXSwibmFtZXMiOlsiY29kZSIsInN0YXRlIiwibm9kZSIsInZhbHVlIiwicHJvcGVydGllcyIsImxhbmciLCJjbGFzc05hbWUiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsImNoaWxkcmVuIiwibWV0YSIsImRhdGEiLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/delete.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strikethrough: () => (/* binding */ strikethrough)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Delete} Delete\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `delete` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Delete} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function strikethrough(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"del\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9kZWxldGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7O0NBSUMsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxjQUFjQyxLQUFLLEVBQUVDLElBQUk7SUFDdkMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9kZWxldGUuanM/Y2U2YiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkRlbGV0ZX0gRGVsZXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBkZWxldGVgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7RGVsZXRlfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gc3RyaWtldGhyb3VnaChzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ2RlbCcsXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IHN0YXRlLmFsbChub2RlKVxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOlsic3RyaWtldGhyb3VnaCIsInN0YXRlIiwibm9kZSIsInJlc3VsdCIsInR5cGUiLCJ0YWdOYW1lIiwicHJvcGVydGllcyIsImNoaWxkcmVuIiwiYWxsIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `emphasis` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Emphasis} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function emphasis(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"em\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Q0FJQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNBLFNBQVNDLEtBQUssRUFBRUMsSUFBSTtJQUNsQyxvQkFBb0IsR0FDcEIsTUFBTUMsU0FBUztRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsWUFBWSxDQUFDO1FBQ2JDLFVBQVVOLE1BQU1PLEdBQUcsQ0FBQ047SUFDdEI7SUFDQUQsTUFBTVEsS0FBSyxDQUFDUCxNQUFNQztJQUNsQixPQUFPRixNQUFNUyxTQUFTLENBQUNSLE1BQU1DO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2VtcGhhc2lzLmpzPzcyOTQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5FbXBoYXNpc30gRW1waGFzaXNcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGVtcGhhc2lzYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0VtcGhhc2lzfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZW1waGFzaXMoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdlbScsXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IHN0YXRlLmFsbChub2RlKVxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOlsiZW1waGFzaXMiLCJzdGF0ZSIsIm5vZGUiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsInByb3BlcnRpZXMiLCJjaGlsZHJlbiIsImFsbCIsInBhdGNoIiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   footnoteReference: () => (/* binding */ footnoteReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function footnoteReference(state, node) {\n    const clobberPrefix = typeof state.options.clobberPrefix === \"string\" ? state.options.clobberPrefix : \"user-content-\";\n    const id = String(node.identifier).toUpperCase();\n    const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(id.toLowerCase());\n    const index = state.footnoteOrder.indexOf(id);\n    /** @type {number} */ let counter;\n    let reuseCounter = state.footnoteCounts.get(id);\n    if (reuseCounter === undefined) {\n        reuseCounter = 0;\n        state.footnoteOrder.push(id);\n        counter = state.footnoteOrder.length;\n    } else {\n        counter = index + 1;\n    }\n    reuseCounter += 1;\n    state.footnoteCounts.set(id, reuseCounter);\n    /** @type {Element} */ const link = {\n        type: \"element\",\n        tagName: \"a\",\n        properties: {\n            href: \"#\" + clobberPrefix + \"fn-\" + safeId,\n            id: clobberPrefix + \"fnref-\" + safeId + (reuseCounter > 1 ? \"-\" + reuseCounter : \"\"),\n            dataFootnoteRef: true,\n            ariaDescribedBy: [\n                \"footnote-label\"\n            ]\n        },\n        children: [\n            {\n                type: \"text\",\n                value: String(counter)\n            }\n        ]\n    };\n    state.patch(node, link);\n    /** @type {Element} */ const sup = {\n        type: \"element\",\n        tagName: \"sup\",\n        properties: {},\n        children: [\n            link\n        ]\n    };\n    state.patch(node, sup);\n    return state.applyData(node, sup);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/heading.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function heading(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"h\" + node.depth,\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9oZWFkaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7OztDQUlDLEdBRUQsbURBQW1EO0FBQ25EO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0EsUUFBUUMsS0FBSyxFQUFFQyxJQUFJO0lBQ2pDLG9CQUFvQixHQUNwQixNQUFNQyxTQUFTO1FBQ2JDLE1BQU07UUFDTkMsU0FBUyxNQUFNSCxLQUFLSSxLQUFLO1FBQ3pCQyxZQUFZLENBQUM7UUFDYkMsVUFBVVAsTUFBTVEsR0FBRyxDQUFDUDtJQUN0QjtJQUNBRCxNQUFNUyxLQUFLLENBQUNSLE1BQU1DO0lBQ2xCLE9BQU9GLE1BQU1VLFNBQVMsQ0FBQ1QsTUFBTUM7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvaGVhZGluZy5qcz9hNzBhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuSGVhZGluZ30gSGVhZGluZ1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgaGVhZGluZ2Agbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtIZWFkaW5nfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaGVhZGluZyhzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ2gnICsgbm9kZS5kZXB0aCxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6WyJoZWFkaW5nIiwic3RhdGUiLCJub2RlIiwicmVzdWx0IiwidHlwZSIsInRhZ05hbWUiLCJkZXB0aCIsInByb3BlcnRpZXMiLCJjaGlsZHJlbiIsImFsbCIsInBhdGNoIiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/html.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Html} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Element | Raw | undefined}\n *   hast node.\n */ function html(state, node) {\n    if (state.options.allowDangerousHtml) {\n        /** @type {Raw} */ const result = {\n            type: \"raw\",\n            value: node.value\n        };\n        state.patch(node, result);\n        return state.applyData(node, result);\n    }\n    return undefined;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9odG1sLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7O0NBVUMsR0FDTSxTQUFTQSxLQUFLQyxLQUFLLEVBQUVDLElBQUk7SUFDOUIsSUFBSUQsTUFBTUUsT0FBTyxDQUFDQyxrQkFBa0IsRUFBRTtRQUNwQyxnQkFBZ0IsR0FDaEIsTUFBTUMsU0FBUztZQUFDQyxNQUFNO1lBQU9DLE9BQU9MLEtBQUtLLEtBQUs7UUFBQTtRQUM5Q04sTUFBTU8sS0FBSyxDQUFDTixNQUFNRztRQUNsQixPQUFPSixNQUFNUSxTQUFTLENBQUNQLE1BQU1HO0lBQy9CO0lBRUEsT0FBT0s7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9odG1sLmpzP2EzNzQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5IdG1sfSBIdG1sXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vLi4vaW5kZXguanMnKS5SYXd9IFJhd1xuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBodG1sYCBub2RlIGludG8gaGFzdCAoYHJhd2Agbm9kZSBpbiBkYW5nZXJvdXMgbW9kZSwgb3RoZXJ3aXNlXG4gKiBub3RoaW5nKS5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0h0bWx9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50IHwgUmF3IHwgdW5kZWZpbmVkfVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBodG1sKHN0YXRlLCBub2RlKSB7XG4gIGlmIChzdGF0ZS5vcHRpb25zLmFsbG93RGFuZ2Vyb3VzSHRtbCkge1xuICAgIC8qKiBAdHlwZSB7UmF3fSAqL1xuICAgIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAncmF3JywgdmFsdWU6IG5vZGUudmFsdWV9XG4gICAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICAgIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxuICB9XG5cbiAgcmV0dXJuIHVuZGVmaW5lZFxufVxuIl0sIm5hbWVzIjpbImh0bWwiLCJzdGF0ZSIsIm5vZGUiLCJvcHRpb25zIiwiYWxsb3dEYW5nZXJvdXNIdG1sIiwicmVzdWx0IiwidHlwZSIsInZhbHVlIiwicGF0Y2giLCJhcHBseURhdGEiLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js":
/*!*************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/* harmony import */ var _revert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../revert.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */ \n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */ function imageReference(state, node) {\n    const id = String(node.identifier).toUpperCase();\n    const definition = state.definitionById.get(id);\n    if (!definition) {\n        return (0,_revert_js__WEBPACK_IMPORTED_MODULE_0__.revert)(state, node);\n    }\n    /** @type {Properties} */ const properties = {\n        src: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__.normalizeUri)(definition.url || \"\"),\n        alt: node.alt\n    };\n    if (definition.title !== null && definition.title !== undefined) {\n        properties.title = definition.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"img\",\n        properties,\n        children: []\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/image.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function image(state, node) {\n    /** @type {Properties} */ const properties = {\n        src: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(node.url)\n    };\n    if (node.alt !== null && node.alt !== undefined) {\n        properties.alt = node.alt;\n    }\n    if (node.title !== null && node.title !== undefined) {\n        properties.title = node.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"img\",\n        properties,\n        children: []\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handlers: () => (/* binding */ handlers)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js\");\n/* harmony import */ var _delete_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./delete.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\");\n/* harmony import */ var _footnote_reference_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./footnote-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./html.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./image.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./link.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./list.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./paragraph.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js\");\n/* harmony import */ var _table_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./table.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js\");\n/* harmony import */ var _table_row_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./table-row.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js\");\n/* harmony import */ var _table_cell_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./table-cell.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default handlers for nodes.\n *\n * @satisfies {import('../state.js').Handlers}\n */ const handlers = {\n    blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n    break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n    code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n    delete: _delete_js__WEBPACK_IMPORTED_MODULE_3__.strikethrough,\n    emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n    footnoteReference: _footnote_reference_js__WEBPACK_IMPORTED_MODULE_5__.footnoteReference,\n    heading: _heading_js__WEBPACK_IMPORTED_MODULE_6__.heading,\n    html: _html_js__WEBPACK_IMPORTED_MODULE_7__.html,\n    imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n    image: _image_js__WEBPACK_IMPORTED_MODULE_9__.image,\n    inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_10__.inlineCode,\n    linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n    link: _link_js__WEBPACK_IMPORTED_MODULE_12__.link,\n    listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n    list: _list_js__WEBPACK_IMPORTED_MODULE_14__.list,\n    paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_15__.paragraph,\n    // @ts-expect-error: root is different, but hard to type.\n    root: _root_js__WEBPACK_IMPORTED_MODULE_16__.root,\n    strong: _strong_js__WEBPACK_IMPORTED_MODULE_17__.strong,\n    table: _table_js__WEBPACK_IMPORTED_MODULE_18__.table,\n    tableCell: _table_cell_js__WEBPACK_IMPORTED_MODULE_19__.tableCell,\n    tableRow: _table_row_js__WEBPACK_IMPORTED_MODULE_20__.tableRow,\n    text: _text_js__WEBPACK_IMPORTED_MODULE_21__.text,\n    thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_22__.thematicBreak,\n    toml: ignore,\n    yaml: ignore,\n    definition: ignore,\n    footnoteDefinition: ignore\n};\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n    return undefined;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function inlineCode(state, node) {\n    /** @type {Text} */ const text = {\n        type: \"text\",\n        value: node.value.replace(/\\r?\\n|\\r/g, \" \")\n    };\n    state.patch(node, text);\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"code\",\n        properties: {},\n        children: [\n            text\n        ]\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/* harmony import */ var _revert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../revert.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */ \n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */ function linkReference(state, node) {\n    const id = String(node.identifier).toUpperCase();\n    const definition = state.definitionById.get(id);\n    if (!definition) {\n        return (0,_revert_js__WEBPACK_IMPORTED_MODULE_0__.revert)(state, node);\n    }\n    /** @type {Properties} */ const properties = {\n        href: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__.normalizeUri)(definition.url || \"\")\n    };\n    if (definition.title !== null && definition.title !== undefined) {\n        properties.title = definition.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"a\",\n        properties,\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9saW5rLXJlZmVyZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7O0NBTUMsR0FFdUQ7QUFDckI7QUFFbkM7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0UsY0FBY0MsS0FBSyxFQUFFQyxJQUFJO0lBQ3ZDLE1BQU1DLEtBQUtDLE9BQU9GLEtBQUtHLFVBQVUsRUFBRUMsV0FBVztJQUM5QyxNQUFNQyxhQUFhTixNQUFNTyxjQUFjLENBQUNDLEdBQUcsQ0FBQ047SUFFNUMsSUFBSSxDQUFDSSxZQUFZO1FBQ2YsT0FBT1Isa0RBQU1BLENBQUNFLE9BQU9DO0lBQ3ZCO0lBRUEsdUJBQXVCLEdBQ3ZCLE1BQU1RLGFBQWE7UUFBQ0MsTUFBTWIseUVBQVlBLENBQUNTLFdBQVdLLEdBQUcsSUFBSTtJQUFHO0lBRTVELElBQUlMLFdBQVdNLEtBQUssS0FBSyxRQUFRTixXQUFXTSxLQUFLLEtBQUtDLFdBQVc7UUFDL0RKLFdBQVdHLEtBQUssR0FBR04sV0FBV00sS0FBSztJQUNyQztJQUVBLG9CQUFvQixHQUNwQixNQUFNRSxTQUFTO1FBQ2JDLE1BQU07UUFDTkMsU0FBUztRQUNUUDtRQUNBUSxVQUFVakIsTUFBTWtCLEdBQUcsQ0FBQ2pCO0lBQ3RCO0lBQ0FELE1BQU1tQixLQUFLLENBQUNsQixNQUFNYTtJQUNsQixPQUFPZCxNQUFNb0IsU0FBUyxDQUFDbkIsTUFBTWE7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvbGluay1yZWZlcmVuY2UuanM/OTlhYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudENvbnRlbnR9IEVsZW1lbnRDb250ZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuUHJvcGVydGllc30gUHJvcGVydGllc1xuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5MaW5rUmVmZXJlbmNlfSBMaW5rUmVmZXJlbmNlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbmltcG9ydCB7bm9ybWFsaXplVXJpfSBmcm9tICdtaWNyb21hcmstdXRpbC1zYW5pdGl6ZS11cmknXG5pbXBvcnQge3JldmVydH0gZnJvbSAnLi4vcmV2ZXJ0LmpzJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGxpbmtSZWZlcmVuY2VgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7TGlua1JlZmVyZW5jZX0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0FycmF5PEVsZW1lbnRDb250ZW50PiB8IEVsZW1lbnRDb250ZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBsaW5rUmVmZXJlbmNlKHN0YXRlLCBub2RlKSB7XG4gIGNvbnN0IGlkID0gU3RyaW5nKG5vZGUuaWRlbnRpZmllcikudG9VcHBlckNhc2UoKVxuICBjb25zdCBkZWZpbml0aW9uID0gc3RhdGUuZGVmaW5pdGlvbkJ5SWQuZ2V0KGlkKVxuXG4gIGlmICghZGVmaW5pdGlvbikge1xuICAgIHJldHVybiByZXZlcnQoc3RhdGUsIG5vZGUpXG4gIH1cblxuICAvKiogQHR5cGUge1Byb3BlcnRpZXN9ICovXG4gIGNvbnN0IHByb3BlcnRpZXMgPSB7aHJlZjogbm9ybWFsaXplVXJpKGRlZmluaXRpb24udXJsIHx8ICcnKX1cblxuICBpZiAoZGVmaW5pdGlvbi50aXRsZSAhPT0gbnVsbCAmJiBkZWZpbml0aW9uLnRpdGxlICE9PSB1bmRlZmluZWQpIHtcbiAgICBwcm9wZXJ0aWVzLnRpdGxlID0gZGVmaW5pdGlvbi50aXRsZVxuICB9XG5cbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdhJyxcbiAgICBwcm9wZXJ0aWVzLFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbIm5vcm1hbGl6ZVVyaSIsInJldmVydCIsImxpbmtSZWZlcmVuY2UiLCJzdGF0ZSIsIm5vZGUiLCJpZCIsIlN0cmluZyIsImlkZW50aWZpZXIiLCJ0b1VwcGVyQ2FzZSIsImRlZmluaXRpb24iLCJkZWZpbml0aW9uQnlJZCIsImdldCIsInByb3BlcnRpZXMiLCJocmVmIiwidXJsIiwidGl0bGUiLCJ1bmRlZmluZWQiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsImNoaWxkcmVuIiwiYWxsIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/link.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function link(state, node) {\n    /** @type {Properties} */ const properties = {\n        href: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(node.url)\n    };\n    if (node.title !== null && node.title !== undefined) {\n        properties.title = node.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"a\",\n        properties,\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/list-item.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */ function listItem(state, node, parent) {\n    const results = state.all(node);\n    const loose = parent ? listLoose(parent) : listItemLoose(node);\n    /** @type {Properties} */ const properties = {};\n    /** @type {Array<ElementContent>} */ const children = [];\n    if (typeof node.checked === \"boolean\") {\n        const head = results[0];\n        /** @type {Element} */ let paragraph;\n        if (head && head.type === \"element\" && head.tagName === \"p\") {\n            paragraph = head;\n        } else {\n            paragraph = {\n                type: \"element\",\n                tagName: \"p\",\n                properties: {},\n                children: []\n            };\n            results.unshift(paragraph);\n        }\n        if (paragraph.children.length > 0) {\n            paragraph.children.unshift({\n                type: \"text\",\n                value: \" \"\n            });\n        }\n        paragraph.children.unshift({\n            type: \"element\",\n            tagName: \"input\",\n            properties: {\n                type: \"checkbox\",\n                checked: node.checked,\n                disabled: true\n            },\n            children: []\n        });\n        // According to github-markdown-css, this class hides bullet.\n        // See: <https://github.com/sindresorhus/github-markdown-css>.\n        properties.className = [\n            \"task-list-item\"\n        ];\n    }\n    let index = -1;\n    while(++index < results.length){\n        const child = results[index];\n        // Add eols before nodes, except if this is a loose, first paragraph.\n        if (loose || index !== 0 || child.type !== \"element\" || child.tagName !== \"p\") {\n            children.push({\n                type: \"text\",\n                value: \"\\n\"\n            });\n        }\n        if (child.type === \"element\" && child.tagName === \"p\" && !loose) {\n            children.push(...child.children);\n        } else {\n            children.push(child);\n        }\n    }\n    const tail = results[results.length - 1];\n    // Add a final eol.\n    if (tail && (loose || tail.type !== \"element\" || tail.tagName !== \"p\")) {\n        children.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"li\",\n        properties,\n        children\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n/**\n * @param {Parents} node\n * @return {Boolean}\n */ function listLoose(node) {\n    let loose = false;\n    if (node.type === \"list\") {\n        loose = node.spread || false;\n        const children = node.children;\n        let index = -1;\n        while(!loose && ++index < children.length){\n            loose = listItemLoose(children[index]);\n        }\n    }\n    return loose;\n}\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */ function listItemLoose(node) {\n    const spread = node.spread;\n    return spread === null || spread === undefined ? node.children.length > 1 : spread;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/list.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function list(state, node) {\n    /** @type {Properties} */ const properties = {};\n    const results = state.all(node);\n    let index = -1;\n    if (typeof node.start === \"number\" && node.start !== 1) {\n        properties.start = node.start;\n    }\n    // Like GitHub, add a class for custom styling.\n    while(++index < results.length){\n        const child = results[index];\n        if (child.type === \"element\" && child.tagName === \"li\" && child.properties && Array.isArray(child.properties.className) && child.properties.className.includes(\"task-list-item\")) {\n            properties.className = [\n                \"contains-task-list\"\n            ];\n            break;\n        }\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: node.ordered ? \"ol\" : \"ul\",\n        properties,\n        children: state.wrap(results, true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function paragraph(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"p\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9wYXJhZ3JhcGguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7O0NBSUMsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxVQUFVQyxLQUFLLEVBQUVDLElBQUk7SUFDbkMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9wYXJhZ3JhcGguanM/ZGUxOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlBhcmFncmFwaH0gUGFyYWdyYXBoXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBwYXJhZ3JhcGhgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7UGFyYWdyYXBofSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcGFyYWdyYXBoKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAncCcsXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IHN0YXRlLmFsbChub2RlKVxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOlsicGFyYWdyYXBoIiwic3RhdGUiLCJub2RlIiwicmVzdWx0IiwidHlwZSIsInRhZ05hbWUiLCJwcm9wZXJ0aWVzIiwiY2hpbGRyZW4iLCJhbGwiLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/root.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Parents} HastParents\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastParents}\n *   hast node.\n */ function root(state, node) {\n    /** @type {HastRoot} */ const result = {\n        type: \"root\",\n        children: state.wrap(state.all(node))\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNBLEtBQUtDLEtBQUssRUFBRUMsSUFBSTtJQUM5QixxQkFBcUIsR0FDckIsTUFBTUMsU0FBUztRQUFDQyxNQUFNO1FBQVFDLFVBQVVKLE1BQU1LLElBQUksQ0FBQ0wsTUFBTU0sR0FBRyxDQUFDTDtJQUFNO0lBQ25FRCxNQUFNTyxLQUFLLENBQUNOLE1BQU1DO0lBQ2xCLE9BQU9GLE1BQU1RLFNBQVMsQ0FBQ1AsTUFBTUM7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvcm9vdC5qcz8wNjIyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLlBhcmVudHN9IEhhc3RQYXJlbnRzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuUm9vdH0gSGFzdFJvb3RcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuUm9vdH0gTWRhc3RSb290XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGByb290YCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge01kYXN0Um9vdH0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0hhc3RQYXJlbnRzfVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByb290KHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7SGFzdFJvb3R9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAncm9vdCcsIGNoaWxkcmVuOiBzdGF0ZS53cmFwKHN0YXRlLmFsbChub2RlKSl9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOlsicm9vdCIsInN0YXRlIiwibm9kZSIsInJlc3VsdCIsInR5cGUiLCJjaGlsZHJlbiIsIndyYXAiLCJhbGwiLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/strong.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `strong` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Strong} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function strong(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"strong\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7O0NBSUMsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxPQUFPQyxLQUFLLEVBQUVDLElBQUk7SUFDaEMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9zdHJvbmcuanM/MTRmNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlN0cm9uZ30gU3Ryb25nXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBzdHJvbmdgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7U3Ryb25nfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gc3Ryb25nKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnc3Ryb25nJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6WyJzdHJvbmciLCJzdGF0ZSIsIm5vZGUiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsInByb3BlcnRpZXMiLCJjaGlsZHJlbiIsImFsbCIsInBhdGNoIiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableCell: () => (/* binding */ tableCell)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function tableCell(state, node) {\n    // Note: this function is normally not called: see `table-row` for how rows\n    // and their cells are compiled.\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"td\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90YWJsZS1jZWxsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7OztDQUlDLEdBRUQsbURBQW1EO0FBQ25EO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0EsVUFBVUMsS0FBSyxFQUFFQyxJQUFJO0lBQ25DLDJFQUEyRTtJQUMzRSxnQ0FBZ0M7SUFDaEMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90YWJsZS1jZWxsLmpzP2Q3NmMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5UYWJsZUNlbGx9IFRhYmxlQ2VsbFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgdGFibGVDZWxsYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge1RhYmxlQ2VsbH0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRhYmxlQ2VsbChzdGF0ZSwgbm9kZSkge1xuICAvLyBOb3RlOiB0aGlzIGZ1bmN0aW9uIGlzIG5vcm1hbGx5IG5vdCBjYWxsZWQ6IHNlZSBgdGFibGUtcm93YCBmb3IgaG93IHJvd3NcbiAgLy8gYW5kIHRoZWlyIGNlbGxzIGFyZSBjb21waWxlZC5cbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICd0ZCcsIC8vIEFzc3VtZSBib2R5IGNlbGwuXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IHN0YXRlLmFsbChub2RlKVxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOlsidGFibGVDZWxsIiwic3RhdGUiLCJub2RlIiwicmVzdWx0IiwidHlwZSIsInRhZ05hbWUiLCJwcm9wZXJ0aWVzIiwiY2hpbGRyZW4iLCJhbGwiLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table-row.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableRow: () => (/* binding */ tableRow)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */ function tableRow(state, node, parent) {\n    const siblings = parent ? parent.children : undefined;\n    // Generate a body row when without parent.\n    const rowIndex = siblings ? siblings.indexOf(node) : 1;\n    const tagName = rowIndex === 0 ? \"th\" : \"td\";\n    // To do: option to use `style`?\n    const align = parent && parent.type === \"table\" ? parent.align : undefined;\n    const length = align ? align.length : node.children.length;\n    let cellIndex = -1;\n    /** @type {Array<ElementContent>} */ const cells = [];\n    while(++cellIndex < length){\n        // Note: can also be undefined.\n        const cell = node.children[cellIndex];\n        /** @type {Properties} */ const properties = {};\n        const alignValue = align ? align[cellIndex] : undefined;\n        if (alignValue) {\n            properties.align = alignValue;\n        }\n        /** @type {Element} */ let result = {\n            type: \"element\",\n            tagName,\n            properties,\n            children: []\n        };\n        if (cell) {\n            result.children = state.all(cell);\n            state.patch(cell, result);\n            result = state.applyData(cell, result);\n        }\n        cells.push(result);\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"tr\",\n        properties: {},\n        children: state.wrap(cells, true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   table: () => (/* binding */ table)\n/* harmony export */ });\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function table(state, node) {\n    const rows = state.all(node);\n    const firstRow = rows.shift();\n    /** @type {Array<Element>} */ const tableContent = [];\n    if (firstRow) {\n        /** @type {Element} */ const head = {\n            type: \"element\",\n            tagName: \"thead\",\n            properties: {},\n            children: state.wrap([\n                firstRow\n            ], true)\n        };\n        state.patch(node.children[0], head);\n        tableContent.push(head);\n    }\n    if (rows.length > 0) {\n        /** @type {Element} */ const body = {\n            type: \"element\",\n            tagName: \"tbody\",\n            properties: {},\n            children: state.wrap(rows, true)\n        };\n        const start = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_0__.pointStart)(node.children[1]);\n        const end = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_0__.pointEnd)(node.children[node.children.length - 1]);\n        if (start && end) body.position = {\n            start,\n            end\n        };\n        tableContent.push(body);\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"table\",\n        properties: {},\n        children: state.wrap(tableContent, true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/text.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var trim_lines__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! trim-lines */ \"(ssr)/./node_modules/trim-lines/index.js\");\n/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastElement | HastText}\n *   hast node.\n */ function text(state, node) {\n    /** @type {HastText} */ const result = {\n        type: \"text\",\n        value: (0,trim_lines__WEBPACK_IMPORTED_MODULE_0__.trimLines)(String(node.value))\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7Ozs7O0NBS0MsR0FFbUM7QUFFcEM7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0MsS0FBS0MsS0FBSyxFQUFFQyxJQUFJO0lBQzlCLHFCQUFxQixHQUNyQixNQUFNQyxTQUFTO1FBQUNDLE1BQU07UUFBUUMsT0FBT04scURBQVNBLENBQUNPLE9BQU9KLEtBQUtHLEtBQUs7SUFBRTtJQUNsRUosTUFBTU0sS0FBSyxDQUFDTCxNQUFNQztJQUNsQixPQUFPRixNQUFNTyxTQUFTLENBQUNOLE1BQU1DO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL3RleHQuanM/YzViMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBIYXN0RWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLlRleHR9IEhhc3RUZXh0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlRleHR9IE1kYXN0VGV4dFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG5pbXBvcnQge3RyaW1MaW5lc30gZnJvbSAndHJpbS1saW5lcydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGB0ZXh0YCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge01kYXN0VGV4dH0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0hhc3RFbGVtZW50IHwgSGFzdFRleHR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRleHQoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtIYXN0VGV4dH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge3R5cGU6ICd0ZXh0JywgdmFsdWU6IHRyaW1MaW5lcyhTdHJpbmcobm9kZS52YWx1ZSkpfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbInRyaW1MaW5lcyIsInRleHQiLCJzdGF0ZSIsIm5vZGUiLCJyZXN1bHQiLCJ0eXBlIiwidmFsdWUiLCJTdHJpbmciLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function thematicBreak(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"hr\",\n        properties: {},\n        children: []\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90aGVtYXRpYy1icmVhay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Q0FJQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNBLGNBQWNDLEtBQUssRUFBRUMsSUFBSTtJQUN2QyxvQkFBb0IsR0FDcEIsTUFBTUMsU0FBUztRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsWUFBWSxDQUFDO1FBQ2JDLFVBQVUsRUFBRTtJQUNkO0lBQ0FOLE1BQU1PLEtBQUssQ0FBQ04sTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVEsU0FBUyxDQUFDUCxNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90aGVtYXRpYy1icmVhay5qcz9mODM3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuVGhlbWF0aWNCcmVha30gVGhlbWF0aWNCcmVha1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgdGhlbWF0aWNCcmVha2Agbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtUaGVtYXRpY0JyZWFrfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gdGhlbWF0aWNCcmVhayhzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ2hyJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogW11cbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbInRoZW1hdGljQnJlYWsiLCJzdGF0ZSIsIm5vZGUiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsInByb3BlcnRpZXMiLCJjaGlsZHJlbiIsInBhdGNoIiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toHast: () => (/* binding */ toHast)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var _footer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./footer.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js\");\n/* harmony import */ var _state_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/state.js\");\n/**\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('./state.js').Options} Options\n */ \n\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {HastNodes}\n *   hast tree.\n */ function toHast(tree, options) {\n    const state = (0,_state_js__WEBPACK_IMPORTED_MODULE_0__.createState)(tree, options);\n    const node = state.one(tree, undefined);\n    const foot = (0,_footer_js__WEBPACK_IMPORTED_MODULE_1__.footer)(state);\n    /** @type {HastNodes} */ const result = Array.isArray(node) ? {\n        type: \"root\",\n        children: node\n    } : node || {\n        type: \"root\",\n        children: []\n    };\n    if (foot) {\n        // If there’s a footer, there were definitions, meaning block\n        // content.\n        // So `result` is a parent node.\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(\"children\" in result);\n        result.children.push({\n            type: \"text\",\n            value: \"\\n\"\n        }, foot);\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js":
/*!*******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/revert.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   revert: () => (/* binding */ revert)\n/* harmony export */ });\n/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Nodes} Nodes\n * @typedef {import('mdast').Reference} Reference\n *\n * @typedef {import('./state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Extract<Nodes, Reference>} node\n *   Reference node (image, link).\n * @returns {Array<ElementContent>}\n *   hast content.\n */ function revert(state, node) {\n    const subtype = node.referenceType;\n    let suffix = \"]\";\n    if (subtype === \"collapsed\") {\n        suffix += \"[]\";\n    } else if (subtype === \"full\") {\n        suffix += \"[\" + (node.label || node.identifier) + \"]\";\n    }\n    if (node.type === \"imageReference\") {\n        return [\n            {\n                type: \"text\",\n                value: \"![\" + node.alt + suffix\n            }\n        ];\n    }\n    const contents = state.all(node);\n    const head = contents[0];\n    if (head && head.type === \"text\") {\n        head.value = \"[\" + head.value;\n    } else {\n        contents.unshift({\n            type: \"text\",\n            value: \"[\"\n        });\n    }\n    const tail = contents[contents.length - 1];\n    if (tail && tail.type === \"text\") {\n        tail.value += suffix;\n    } else {\n        contents.push({\n            type: \"text\",\n            value: suffix\n        });\n    }\n    return contents;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/state.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/state.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createState: () => (/* binding */ createState),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var _handlers_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handlers/index.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js\");\n/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').RootContent} HastRootContent\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('mdast').Parents} MdastParents\n *\n * @typedef {import('vfile').VFile} VFile\n *\n * @typedef {import('./footer.js').FootnoteBackContentTemplate} FootnoteBackContentTemplate\n * @typedef {import('./footer.js').FootnoteBackLabelTemplate} FootnoteBackLabelTemplate\n */ /**\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<HastElementContent> | HastElementContent | undefined}\n *   hast node.\n *\n * @typedef {Partial<Record<MdastNodes['type'], Handler>>} Handlers\n *   Handle nodes.\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree (default:\n *   `false`).\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` property on footnotes to prevent them from\n *   *clobbering* (default: `'user-content-'`).\n *\n *   Pass `''` for trusted markdown and when you are careful with\n *   polyfilling.\n *   You could pass a different prefix.\n *\n *   DOM clobbering is this:\n *\n *   ```html\n *   <p id=\"x\"></p>\n *   <script>alert(x) // `x` now refers to the `p#x` DOM element</script>\n *   ```\n *\n *   The above example shows that elements are made available by browsers, by\n *   their ID, on the `window` object.\n *   This is a security risk because you might be expecting some other variable\n *   at that place.\n *   It can also break polyfills.\n *   Using a prefix solves these problems.\n * @property {VFile | null | undefined} [file]\n *   Corresponding virtual file representing the input document (optional).\n * @property {FootnoteBackContentTemplate | string | null | undefined} [footnoteBackContent]\n *   Content of the backreference back to references (default: `defaultFootnoteBackContent`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackContent(_, rereferenceIndex) {\n *     const result = [{type: 'text', value: '↩'}]\n *\n *     if (rereferenceIndex > 1) {\n *       result.push({\n *         type: 'element',\n *         tagName: 'sup',\n *         properties: {},\n *         children: [{type: 'text', value: String(rereferenceIndex)}]\n *       })\n *     }\n *\n *     return result\n *   }\n *   ```\n *\n *   This content is used in the `a` element of each backreference (the `↩`\n *   links).\n * @property {FootnoteBackLabelTemplate | string | null | undefined} [footnoteBackLabel]\n *   Label to describe the backreference back to references (default:\n *   `defaultFootnoteBackLabel`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n *    return (\n *      'Back to reference ' +\n *      (referenceIndex + 1) +\n *      (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n *    )\n *   }\n *   ```\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is used in the `ariaLabel` property on each backreference\n *   (the `↩` links).\n *   It affects users of assistive technology.\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Textual label to use for the footnotes section (default: `'Footnotes'`).\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (default: `{className:\n *   ['sr-only']}`).\n *\n *   Change it to show the label and add other properties.\n *\n *   This label is typically hidden visually (assuming an `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass an empty string.\n *   You can also add different properties.\n *\n *   > **Note**: `id: 'footnote-label'` is always added, because footnote\n *   > calls use it with `aria-describedby` to provide an accessible label.\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   HTML tag name to use for the footnote label element (default: `'h2'`).\n *\n *   Change it to match your document structure.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes (optional).\n * @property {Array<MdastNodes['type']> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed)\n *   (optional).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes (optional).\n *\n * @typedef State\n *   Info passed around.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => HastElement | Type} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {Map<string, MdastDefinition>} definitionById\n *   Definitions by their identifier.\n * @property {Map<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Map<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {(node: MdastNodes, parent: MdastParents | undefined) => Array<HastElementContent> | HastElementContent | undefined} one\n *   Transform an mdast node to hast.\n * @property {Options} options\n *   Configuration.\n * @property {(from: MdastNodes, node: HastNodes) => undefined} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastRootContent>(nodes: Array<Type>, loose?: boolean | undefined) => Array<HastText | Type>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n */ \n\n\n\nconst own = {}.hasOwnProperty;\n/** @type {Options} */ const emptyOptions = {};\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {State}\n *   `state` function.\n */ function createState(tree, options) {\n    const settings = options || emptyOptions;\n    /** @type {Map<string, MdastDefinition>} */ const definitionById = new Map();\n    /** @type {Map<string, MdastFootnoteDefinition>} */ const footnoteById = new Map();\n    /** @type {Map<string, number>} */ const footnoteCounts = new Map();\n    /** @type {Handlers} */ // @ts-expect-error: the root handler returns a root.\n    // Hard to type.\n    const handlers = {\n        ..._handlers_index_js__WEBPACK_IMPORTED_MODULE_0__.handlers,\n        ...settings.handlers\n    };\n    /** @type {State} */ const state = {\n        all,\n        applyData,\n        definitionById,\n        footnoteById,\n        footnoteCounts,\n        footnoteOrder: [],\n        handlers,\n        one,\n        options: settings,\n        patch,\n        wrap\n    };\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(tree, function(node) {\n        if (node.type === \"definition\" || node.type === \"footnoteDefinition\") {\n            const map = node.type === \"definition\" ? definitionById : footnoteById;\n            const id = String(node.identifier).toUpperCase();\n            // Mimick CM behavior of link definitions.\n            // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/9032189/lib/index.js#L20-L21>.\n            if (!map.has(id)) {\n                // @ts-expect-error: node type matches map.\n                map.set(id, node);\n            }\n        }\n    });\n    return state;\n    /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {Array<HastElementContent> | HastElementContent | undefined}\n   *   Resulting hast node.\n   */ function one(node, parent) {\n        const type = node.type;\n        const handle = state.handlers[type];\n        if (own.call(state.handlers, type) && handle) {\n            return handle(state, node, parent);\n        }\n        if (state.options.passThrough && state.options.passThrough.includes(type)) {\n            if (\"children\" in node) {\n                const { children, ...shallow } = node;\n                const result = (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(shallow);\n                // @ts-expect-error: TS doesn’t understand…\n                result.children = state.all(node);\n                // @ts-expect-error: TS doesn’t understand…\n                return result;\n            }\n            // @ts-expect-error: it’s custom.\n            return (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node);\n        }\n        const unknown = state.options.unknownHandler || defaultUnknownHandler;\n        return unknown(state, node, parent);\n    }\n    /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */ function all(parent) {\n        /** @type {Array<HastElementContent>} */ const values = [];\n        if (\"children\" in parent) {\n            const nodes = parent.children;\n            let index = -1;\n            while(++index < nodes.length){\n                const result = state.one(nodes[index], parent);\n                // To do: see if we van clean this? Can we merge texts?\n                if (result) {\n                    if (index && nodes[index - 1].type === \"break\") {\n                        if (!Array.isArray(result) && result.type === \"text\") {\n                            result.value = trimMarkdownSpaceStart(result.value);\n                        }\n                        if (!Array.isArray(result) && result.type === \"element\") {\n                            const head = result.children[0];\n                            if (head && head.type === \"text\") {\n                                head.value = trimMarkdownSpaceStart(head.value);\n                            }\n                        }\n                    }\n                    if (Array.isArray(result)) {\n                        values.push(...result);\n                    } else {\n                        values.push(result);\n                    }\n                }\n            }\n        }\n        return values;\n    }\n}\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */ function patch(from, to) {\n    if (from.position) to.position = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_3__.position)(from);\n}\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {HastElement | Type}\n *   Nothing.\n */ function applyData(from, to) {\n    /** @type {HastElement | Type} */ let result = to;\n    // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n    if (from && from.data) {\n        const hName = from.data.hName;\n        const hChildren = from.data.hChildren;\n        const hProperties = from.data.hProperties;\n        if (typeof hName === \"string\") {\n            // Transforming the node resulted in an element with a different name\n            // than wanted:\n            if (result.type === \"element\") {\n                result.tagName = hName;\n            } else {\n                /** @type {Array<HastElementContent>} */ // @ts-expect-error: assume no doctypes in `root`.\n                const children = \"children\" in result ? result.children : [\n                    result\n                ];\n                result = {\n                    type: \"element\",\n                    tagName: hName,\n                    properties: {},\n                    children\n                };\n            }\n        }\n        if (result.type === \"element\" && hProperties) {\n            Object.assign(result.properties, (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(hProperties));\n        }\n        if (\"children\" in result && result.children && hChildren !== null && hChildren !== undefined) {\n            result.children = hChildren;\n        }\n    }\n    return result;\n}\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastElement | HastText}\n *   Resulting hast node.\n */ function defaultUnknownHandler(state, node) {\n    const data = node.data || {};\n    /** @type {HastElement | HastText} */ const result = \"value\" in node && !(own.call(data, \"hProperties\") || own.call(data, \"hChildren\")) ? {\n        type: \"text\",\n        value: node.value\n    } : {\n        type: \"element\",\n        tagName: \"div\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastRootContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | undefined} [loose=false]\n *   Whether to add line endings at start and end (default: `false`).\n * @returns {Array<HastText | Type>}\n *   Wrapped nodes.\n */ function wrap(nodes, loose) {\n    /** @type {Array<HastText | Type>} */ const result = [];\n    let index = -1;\n    if (loose) {\n        result.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n    }\n    while(++index < nodes.length){\n        if (index) result.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n        result.push(nodes[index]);\n    }\n    if (loose && nodes.length > 0) {\n        result.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n    }\n    return result;\n}\n/**\n * Trim spaces and tabs at the start of `value`.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Result.\n */ function trimMarkdownSpaceStart(value) {\n    let index = 0;\n    let code = value.charCodeAt(index);\n    while(code === 9 || code === 32){\n        index++;\n        code = value.charCodeAt(index);\n    }\n    return value.slice(index);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/state.js\n");

/***/ })

};
;