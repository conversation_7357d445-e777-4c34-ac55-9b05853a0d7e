"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zustand";
exports.ids = ["vendor-chunks/zustand"];
exports.modules = {

/***/ "(ssr)/./node_modules/zustand/esm/react.mjs":
/*!********************************************!*\
  !*** ./node_modules/zustand/esm/react.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n\n\nconst identity = (arg)=>arg;\nfunction useStore(api, selector = identity) {\n    const slice = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(api.subscribe, ()=>selector(api.getState()), ()=>selector(api.getInitialState()));\n    react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue(slice);\n    return slice;\n}\nconst createImpl = (createState)=>{\n    const api = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__.createStore)(createState);\n    const useBoundStore = (selector)=>useStore(api, selector);\n    Object.assign(useBoundStore, api);\n    return useBoundStore;\n};\nconst create = (createState)=>createState ? createImpl(createState) : createImpl;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvenVzdGFuZC9lc20vcmVhY3QubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFDb0I7QUFFOUMsTUFBTUUsV0FBVyxDQUFDQyxNQUFRQTtBQUMxQixTQUFTQyxTQUFTQyxHQUFHLEVBQUVDLFdBQVdKLFFBQVE7SUFDeEMsTUFBTUssUUFBUVAsdURBQTBCLENBQ3RDSyxJQUFJSSxTQUFTLEVBQ2IsSUFBTUgsU0FBU0QsSUFBSUssUUFBUSxLQUMzQixJQUFNSixTQUFTRCxJQUFJTSxlQUFlO0lBRXBDWCxnREFBbUIsQ0FBQ087SUFDcEIsT0FBT0E7QUFDVDtBQUNBLE1BQU1NLGFBQWEsQ0FBQ0M7SUFDbEIsTUFBTVQsTUFBTUosNERBQVdBLENBQUNhO0lBQ3hCLE1BQU1DLGdCQUFnQixDQUFDVCxXQUFhRixTQUFTQyxLQUFLQztJQUNsRFUsT0FBT0MsTUFBTSxDQUFDRixlQUFlVjtJQUM3QixPQUFPVTtBQUNUO0FBQ0EsTUFBTUcsU0FBUyxDQUFDSixjQUFnQkEsY0FBY0QsV0FBV0MsZUFBZUQ7QUFFNUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL3p1c3RhbmQvZXNtL3JlYWN0Lm1qcz9hYTljIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBjcmVhdGVTdG9yZSB9IGZyb20gJ3p1c3RhbmQvdmFuaWxsYSc7XG5cbmNvbnN0IGlkZW50aXR5ID0gKGFyZykgPT4gYXJnO1xuZnVuY3Rpb24gdXNlU3RvcmUoYXBpLCBzZWxlY3RvciA9IGlkZW50aXR5KSB7XG4gIGNvbnN0IHNsaWNlID0gUmVhY3QudXNlU3luY0V4dGVybmFsU3RvcmUoXG4gICAgYXBpLnN1YnNjcmliZSxcbiAgICAoKSA9PiBzZWxlY3RvcihhcGkuZ2V0U3RhdGUoKSksXG4gICAgKCkgPT4gc2VsZWN0b3IoYXBpLmdldEluaXRpYWxTdGF0ZSgpKVxuICApO1xuICBSZWFjdC51c2VEZWJ1Z1ZhbHVlKHNsaWNlKTtcbiAgcmV0dXJuIHNsaWNlO1xufVxuY29uc3QgY3JlYXRlSW1wbCA9IChjcmVhdGVTdGF0ZSkgPT4ge1xuICBjb25zdCBhcGkgPSBjcmVhdGVTdG9yZShjcmVhdGVTdGF0ZSk7XG4gIGNvbnN0IHVzZUJvdW5kU3RvcmUgPSAoc2VsZWN0b3IpID0+IHVzZVN0b3JlKGFwaSwgc2VsZWN0b3IpO1xuICBPYmplY3QuYXNzaWduKHVzZUJvdW5kU3RvcmUsIGFwaSk7XG4gIHJldHVybiB1c2VCb3VuZFN0b3JlO1xufTtcbmNvbnN0IGNyZWF0ZSA9IChjcmVhdGVTdGF0ZSkgPT4gY3JlYXRlU3RhdGUgPyBjcmVhdGVJbXBsKGNyZWF0ZVN0YXRlKSA6IGNyZWF0ZUltcGw7XG5cbmV4cG9ydCB7IGNyZWF0ZSwgdXNlU3RvcmUgfTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZVN0b3JlIiwiaWRlbnRpdHkiLCJhcmciLCJ1c2VTdG9yZSIsImFwaSIsInNlbGVjdG9yIiwic2xpY2UiLCJ1c2VTeW5jRXh0ZXJuYWxTdG9yZSIsInN1YnNjcmliZSIsImdldFN0YXRlIiwiZ2V0SW5pdGlhbFN0YXRlIiwidXNlRGVidWdWYWx1ZSIsImNyZWF0ZUltcGwiLCJjcmVhdGVTdGF0ZSIsInVzZUJvdW5kU3RvcmUiLCJPYmplY3QiLCJhc3NpZ24iLCJjcmVhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/react.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ./node_modules/zustand/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore)\n/* harmony export */ });\nconst createStoreImpl = (createState)=>{\n    let state;\n    const listeners = /* @__PURE__ */ new Set();\n    const setState = (partial, replace)=>{\n        const nextState = typeof partial === \"function\" ? partial(state) : partial;\n        if (!Object.is(nextState, state)) {\n            const previousState = state;\n            state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n            listeners.forEach((listener)=>listener(state, previousState));\n        }\n    };\n    const getState = ()=>state;\n    const getInitialState = ()=>initialState;\n    const subscribe = (listener)=>{\n        listeners.add(listener);\n        return ()=>listeners.delete(listener);\n    };\n    const api = {\n        setState,\n        getState,\n        getInitialState,\n        subscribe\n    };\n    const initialState = state = createState(setState, getState, api);\n    return api;\n};\nconst createStore = (createState)=>createState ? createStoreImpl(createState) : createStoreImpl;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/vanilla.mjs\n");

/***/ })

};
;