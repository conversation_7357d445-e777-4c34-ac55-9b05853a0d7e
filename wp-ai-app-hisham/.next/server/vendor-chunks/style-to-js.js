"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/style-to-js";
exports.ids = ["vendor-chunks/style-to-js"];
exports.modules = {

/***/ "(ssr)/./node_modules/style-to-js/cjs/index.js":
/*!***********************************************!*\
  !*** ./node_modules/style-to-js/cjs/index.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nvar style_to_object_1 = __importDefault(__webpack_require__(/*! style-to-object */ \"(ssr)/./node_modules/style-to-object/cjs/index.js\"));\nvar utilities_1 = __webpack_require__(/*! ./utilities */ \"(ssr)/./node_modules/style-to-js/cjs/utilities.js\");\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */ function StyleToJS(style, options) {\n    var output = {};\n    if (!style || typeof style !== \"string\") {\n        return output;\n    }\n    (0, style_to_object_1.default)(style, function(property, value) {\n        // skip CSS comment\n        if (property && value) {\n            output[(0, utilities_1.camelCase)(property, options)] = value;\n        }\n    });\n    return output;\n}\nStyleToJS.default = StyleToJS;\nmodule.exports = StyleToJS; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/style-to-js/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/style-to-js/cjs/utilities.js":
/*!***************************************************!*\
  !*** ./node_modules/style-to-js/cjs/utilities.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.camelCase = void 0;\nvar CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9_-]+$/;\nvar HYPHEN_REGEX = /-([a-z])/g;\nvar NO_HYPHEN_REGEX = /^[^-]+$/;\nvar VENDOR_PREFIX_REGEX = /^-(webkit|moz|ms|o|khtml)-/;\nvar MS_VENDOR_PREFIX_REGEX = /^-(ms)-/;\n/**\n * Checks whether to skip camelCase.\n */ var skipCamelCase = function(property) {\n    return !property || NO_HYPHEN_REGEX.test(property) || CUSTOM_PROPERTY_REGEX.test(property);\n};\n/**\n * Replacer that capitalizes first character.\n */ var capitalize = function(match, character) {\n    return character.toUpperCase();\n};\n/**\n * Replacer that removes beginning hyphen of vendor prefix property.\n */ var trimHyphen = function(match, prefix) {\n    return \"\".concat(prefix, \"-\");\n};\n/**\n * CamelCases a CSS property.\n */ var camelCase = function(property, options) {\n    if (options === void 0) {\n        options = {};\n    }\n    if (skipCamelCase(property)) {\n        return property;\n    }\n    property = property.toLowerCase();\n    if (options.reactCompat) {\n        // `-ms` vendor prefix should not be capitalized\n        property = property.replace(MS_VENDOR_PREFIX_REGEX, trimHyphen);\n    } else {\n        // for non-React, remove first hyphen so vendor prefix is not capitalized\n        property = property.replace(VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    return property.replace(HYPHEN_REGEX, capitalize);\n};\nexports.camelCase = camelCase; //# sourceMappingURL=utilities.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/style-to-js/cjs/utilities.js\n");

/***/ })

};
;