"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-autolink-literal";
exports.ids = ["vendor-chunks/micromark-extension-gfm-autolink-literal"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmAutolinkLiteralHtml: () => (/* binding */ gfmAutolinkLiteralHtml)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @import {CompileContext, Handle, HtmlExtension, Token} from 'micromark-util-types'\n */ \n/**\n * Create an HTML extension for `micromark` to support GitHub autolink literal\n * when serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub autolink literal when serializing to HTML.\n */ function gfmAutolinkLiteralHtml() {\n    return {\n        exit: {\n            literalAutolinkEmail,\n            literalAutolinkHttp,\n            literalAutolinkWww\n        }\n    };\n}\n/**\n * @this {CompileContext}\n * @type {Handle}\n */ function literalAutolinkWww(token) {\n    anchorFromToken.call(this, token, \"http://\");\n}\n/**\n * @this {CompileContext}\n * @type {Handle}\n */ function literalAutolinkEmail(token) {\n    anchorFromToken.call(this, token, \"mailto:\");\n}\n/**\n * @this {CompileContext}\n * @type {Handle}\n */ function literalAutolinkHttp(token) {\n    anchorFromToken.call(this, token);\n}\n/**\n * @this CompileContext\n * @param {Token} token\n * @param {string | null | undefined} [protocol]\n * @returns {undefined}\n */ function anchorFromToken(token, protocol) {\n    const url = this.sliceSerialize(token);\n    this.tag('<a href=\"' + (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.sanitizeUri)((protocol || \"\") + url) + '\">');\n    this.raw(this.encode(url));\n    this.tag(\"</a>\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmAutolinkLiteral: () => (/* binding */ gfmAutolinkLiteral)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {Code, ConstructRecord, Event, Extension, Previous, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */ \n\nconst wwwPrefix = {\n    tokenize: tokenizeWwwPrefix,\n    partial: true\n};\nconst domain = {\n    tokenize: tokenizeDomain,\n    partial: true\n};\nconst path = {\n    tokenize: tokenizePath,\n    partial: true\n};\nconst trail = {\n    tokenize: tokenizeTrail,\n    partial: true\n};\nconst emailDomainDotTrail = {\n    tokenize: tokenizeEmailDomainDotTrail,\n    partial: true\n};\nconst wwwAutolink = {\n    name: \"wwwAutolink\",\n    tokenize: tokenizeWwwAutolink,\n    previous: previousWww\n};\nconst protocolAutolink = {\n    name: \"protocolAutolink\",\n    tokenize: tokenizeProtocolAutolink,\n    previous: previousProtocol\n};\nconst emailAutolink = {\n    name: \"emailAutolink\",\n    tokenize: tokenizeEmailAutolink,\n    previous: previousEmail\n};\n/** @type {ConstructRecord} */ const text = {};\n/**\n * Create an extension for `micromark` to support GitHub autolink literal\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   autolink literal syntax.\n */ function gfmAutolinkLiteral() {\n    return {\n        text\n    };\n}\n/** @type {Code} */ let code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit0;\n// Add alphanumerics.\nwhile(code < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftCurlyBrace){\n    text[code] = emailAutolink;\n    code++;\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseA;\n    else if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket) code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseA;\n}\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign] = emailAutolink;\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash] = emailAutolink;\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot] = emailAutolink;\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore] = emailAutolink;\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseH] = [\n    emailAutolink,\n    protocolAutolink\n];\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseH] = [\n    emailAutolink,\n    protocolAutolink\n];\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW] = [\n    emailAutolink,\n    wwwAutolink\n];\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW] = [\n    emailAutolink,\n    wwwAutolink\n];\n// To do: perform email autolink literals on events, afterwards.\n// That’s where `markdown-rs` and `cmark-gfm` perform it.\n// It should look for `@`, then for atext backwards, and then for a label\n// forwards.\n// To do: `mailto:`, `xmpp:` protocol as prefix.\n/**\n * Email autolink literal.\n *\n * ```markdown\n * > | a <EMAIL> b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeEmailAutolink(effects, ok, nok) {\n    const self = this;\n    /** @type {boolean | undefined} */ let dot;\n    /** @type {boolean} */ let data;\n    return start;\n    /**\n   * Start of email autolink literal.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function start(code) {\n        if (!gfmAtext(code) || !previousEmail.call(self, self.previous) || previousUnbalanced(self.events)) {\n            return nok(code);\n        }\n        effects.enter(\"literalAutolink\");\n        effects.enter(\"literalAutolinkEmail\");\n        return atext(code);\n    }\n    /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function atext(code) {\n        if (gfmAtext(code)) {\n            effects.consume(code);\n            return atext;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.atSign) {\n            effects.consume(code);\n            return emailDomain;\n        }\n        return nok(code);\n    }\n    /**\n   * In email domain.\n   *\n   * The reference code is a bit overly complex as it handles the `@`, of which\n   * there may be just one.\n   * Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L318>\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */ function emailDomain(code) {\n        // Dot followed by alphanumerical (not `-` or `_`).\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot) {\n            return effects.check(emailDomainDotTrail, emailDomainAfter, emailDomainDot)(code);\n        }\n        // Alphanumerical, `-`, and `_`.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code)) {\n            data = true;\n            effects.consume(code);\n            return emailDomain;\n        }\n        // To do: `/` if xmpp.\n        // Note: normally we’d truncate trailing punctuation from the link.\n        // However, email autolink literals cannot contain any of those markers,\n        // except for `.`, but that can only occur if it isn’t trailing.\n        // So we can ignore truncating!\n        return emailDomainAfter(code);\n    }\n    /**\n   * In email domain, on dot that is not a trail.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                      ^\n   * ```\n   *\n   * @type {State}\n   */ function emailDomainDot(code) {\n        effects.consume(code);\n        dot = true;\n        return emailDomain;\n    }\n    /**\n   * After email domain.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */ function emailDomainAfter(code) {\n        // Domain must not be empty, must include a dot, and must end in alphabetical.\n        // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L332>.\n        if (data && dot && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(self.previous)) {\n            effects.exit(\"literalAutolinkEmail\");\n            effects.exit(\"literalAutolink\");\n            return ok(code);\n        }\n        return nok(code);\n    }\n}\n/**\n * `www` autolink literal.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeWwwAutolink(effects, ok, nok) {\n    const self = this;\n    return wwwStart;\n    /**\n   * Start of www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */ function wwwStart(code) {\n        if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW && code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW || !previousWww.call(self, self.previous) || previousUnbalanced(self.events)) {\n            return nok(code);\n        }\n        effects.enter(\"literalAutolink\");\n        effects.enter(\"literalAutolinkWww\");\n        // Note: we *check*, so we can discard the `www.` we parsed.\n        // If it worked, we consider it as a part of the domain.\n        return effects.check(wwwPrefix, effects.attempt(domain, effects.attempt(path, wwwAfter), nok), nok)(code);\n    }\n    /**\n   * After a www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */ function wwwAfter(code) {\n        effects.exit(\"literalAutolinkWww\");\n        effects.exit(\"literalAutolink\");\n        return ok(code);\n    }\n}\n/**\n * Protocol autolink literal.\n *\n * ```markdown\n * > | a https://example.org b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeProtocolAutolink(effects, ok, nok) {\n    const self = this;\n    let buffer = \"\";\n    let seen = false;\n    return protocolStart;\n    /**\n   * Start of protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */ function protocolStart(code) {\n        if ((code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseH || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseH) && previousProtocol.call(self, self.previous) && !previousUnbalanced(self.events)) {\n            effects.enter(\"literalAutolink\");\n            effects.enter(\"literalAutolinkHttp\");\n            buffer += String.fromCodePoint(code);\n            effects.consume(code);\n            return protocolPrefixInside;\n        }\n        return nok(code);\n    }\n    /**\n   * In protocol.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^^^^^\n   * ```\n   *\n   * @type {State}\n   */ function protocolPrefixInside(code) {\n        // `5` is size of `https`\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code) && buffer.length < 5) {\n            // @ts-expect-error: definitely number.\n            buffer += String.fromCodePoint(code);\n            effects.consume(code);\n            return protocolPrefixInside;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) {\n            const protocol = buffer.toLowerCase();\n            if (protocol === \"http\" || protocol === \"https\") {\n                effects.consume(code);\n                return protocolSlashesInside;\n            }\n        }\n        return nok(code);\n    }\n    /**\n   * In slashes.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *           ^^\n   * ```\n   *\n   * @type {State}\n   */ function protocolSlashesInside(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.slash) {\n            effects.consume(code);\n            if (seen) {\n                return afterProtocol;\n            }\n            seen = true;\n            return protocolSlashesInside;\n        }\n        return nok(code);\n    }\n    /**\n   * After protocol, before domain.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */ function afterProtocol(code) {\n        // To do: this is different from `markdown-rs`:\n        // https://github.com/wooorm/markdown-rs/blob/b3a921c761309ae00a51fe348d8a43adbc54b518/src/construct/gfm_autolink_literal.rs#L172-L182\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiControl)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuation)(code) ? nok(code) : effects.attempt(domain, effects.attempt(path, protocolAfter), nok)(code);\n    }\n    /**\n   * After a protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *                              ^\n   * ```\n   *\n   * @type {State}\n   */ function protocolAfter(code) {\n        effects.exit(\"literalAutolinkHttp\");\n        effects.exit(\"literalAutolink\");\n        return ok(code);\n    }\n}\n/**\n * `www` prefix.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeWwwPrefix(effects, ok, nok) {\n    let size = 0;\n    return wwwPrefixInside;\n    /**\n   * In www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *     ^^^^\n   * ```\n   *\n   * @type {State}\n   */ function wwwPrefixInside(code) {\n        if ((code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW) && size < 3) {\n            size++;\n            effects.consume(code);\n            return wwwPrefixInside;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot && size === 3) {\n            effects.consume(code);\n            return wwwPrefixAfter;\n        }\n        return nok(code);\n    }\n    /**\n   * After www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function wwwPrefixAfter(code) {\n        // If there is *anything*, we can link.\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ? nok(code) : ok(code);\n    }\n}\n/**\n * Domain.\n *\n * ```markdown\n * > | a https://example.org b\n *               ^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeDomain(effects, ok, nok) {\n    /** @type {boolean | undefined} */ let underscoreInLastSegment;\n    /** @type {boolean | undefined} */ let underscoreInLastLastSegment;\n    /** @type {boolean | undefined} */ let seen;\n    return domainInside;\n    /**\n   * In domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *             ^^^^^^^^^^^\n   * ```\n   *\n   * @type {State}\n   */ function domainInside(code) {\n        // Check whether this marker, which is a trailing punctuation\n        // marker, optionally followed by more trailing markers, and then\n        // followed by an end.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore) {\n            return effects.check(trail, domainAfter, domainAtPunctuation)(code);\n        }\n        // GH documents that only alphanumerics (other than `-`, `.`, and `_`) can\n        // occur, which sounds like ASCII only, but they also support `www.點看.com`,\n        // so that’s Unicode.\n        // Instead of some new production for Unicode alphanumerics, markdown\n        // already has that for Unicode punctuation and whitespace, so use those.\n        // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L12>.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code) || code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuation)(code)) {\n            return domainAfter(code);\n        }\n        seen = true;\n        effects.consume(code);\n        return domainInside;\n    }\n    /**\n   * In domain, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */ function domainAtPunctuation(code) {\n        // There is an underscore in the last segment of the domain\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore) {\n            underscoreInLastSegment = true;\n        } else {\n            underscoreInLastLastSegment = underscoreInLastSegment;\n            underscoreInLastSegment = undefined;\n        }\n        effects.consume(code);\n        return domainInside;\n    }\n    /**\n   * After domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^\n   * ```\n   *\n   * @type {State} */ function domainAfter(code) {\n        // Note: that’s GH says a dot is needed, but it’s not true:\n        // <https://github.com/github/cmark-gfm/issues/279>\n        if (underscoreInLastLastSegment || underscoreInLastSegment || !seen) {\n            return nok(code);\n        }\n        return ok(code);\n    }\n}\n/**\n * Path.\n *\n * ```markdown\n * > | a https://example.org/stuff b\n *                          ^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizePath(effects, ok) {\n    let sizeOpen = 0;\n    let sizeClose = 0;\n    return pathInside;\n    /**\n   * In path.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^^\n   * ```\n   *\n   * @type {State}\n   */ function pathInside(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis) {\n            sizeOpen++;\n            effects.consume(code);\n            return pathInside;\n        }\n        // To do: `markdown-rs` also needs this.\n        // If this is a paren, and there are less closings than openings,\n        // we don’t check for a trail.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis && sizeClose < sizeOpen) {\n            return pathAtPunctuation(code);\n        }\n        // Check whether this trailing punctuation marker is optionally\n        // followed by more trailing markers, and then followed\n        // by an end.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.quotationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.apostrophe || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.comma || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.questionMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde) {\n            return effects.check(trail, ok, pathAtPunctuation)(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)) {\n            return ok(code);\n        }\n        effects.consume(code);\n        return pathInside;\n    }\n    /**\n   * In path, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com/a\"b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */ function pathAtPunctuation(code) {\n        // Count closing parens.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis) {\n            sizeClose++;\n        }\n        effects.consume(code);\n        return pathInside;\n    }\n}\n/**\n * Trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the entire trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | https://example.com\").\n *                        ^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeTrail(effects, ok, nok) {\n    return trail;\n    /**\n   * In trail of domain or path.\n   *\n   * ```markdown\n   * > | https://example.com\").\n   *                        ^\n   * ```\n   *\n   * @type {State}\n   */ function trail(code) {\n        // Regular trailing punctuation.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.quotationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.apostrophe || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.comma || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.questionMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde) {\n            effects.consume(code);\n            return trail;\n        }\n        // `&` followed by one or more alphabeticals and then a `;`, is\n        // as a whole considered as trailing punctuation.\n        // In all other cases, it is considered as continuation of the URL.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand) {\n            effects.consume(code);\n            return trailCharacterReferenceStart;\n        }\n        // Needed because we allow literals after `[`, as we fix:\n        // <https://github.com/github/cmark-gfm/issues/278>.\n        // Check that it is not followed by `(` or `[`.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n            effects.consume(code);\n            return trailBracketAfter;\n        }\n        if (// `<` is an end.\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan || // So is whitespace.\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)) {\n            return ok(code);\n        }\n        return nok(code);\n    }\n    /**\n   * In trail, after `]`.\n   *\n   * > 👉 **Note**: this deviates from `cmark-gfm` to fix a bug.\n   * > See end of <https://github.com/github/cmark-gfm/issues/278> for more.\n   *\n   * ```markdown\n   * > | https://example.com](\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */ function trailBracketAfter(code) {\n        // Whitespace or something that could start a resource or reference is the end.\n        // Switch back to trail otherwise.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)) {\n            return ok(code);\n        }\n        return trail(code);\n    }\n    /**\n   * In character-reference like trail, after `&`.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */ function trailCharacterReferenceStart(code) {\n        // When non-alpha, it’s not a trail.\n        return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code) ? trailCharacterReferenceInside(code) : nok(code);\n    }\n    /**\n   * In character-reference like trail.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */ function trailCharacterReferenceInside(code) {\n        // Switch back to trail if this is well-formed.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon) {\n            effects.consume(code);\n            return trail;\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code)) {\n            effects.consume(code);\n            return trailCharacterReferenceInside;\n        }\n        // It’s not a trail.\n        return nok(code);\n    }\n}\n/**\n * Dot in email domain trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | <EMAIL>.\n *                        ^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeEmailDomainDotTrail(effects, ok, nok) {\n    return start;\n    /**\n   * Dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                    ^   ^\n   * ```\n   *\n   * @type {State}\n   */ function start(code) {\n        // Must be dot.\n        effects.consume(code);\n        return after;\n    }\n    /**\n   * After dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                     ^   ^\n   * ```\n   *\n   * @type {State}\n   */ function after(code) {\n        // Not a trail if alphanumeric.\n        return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code) ? nok(code) : ok(code);\n    }\n}\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L156>.\n *\n * @type {Previous}\n */ function previousWww(code) {\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code);\n}\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L214>.\n *\n * @type {Previous}\n */ function previousProtocol(code) {\n    return !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code);\n}\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */ function previousEmail(code) {\n    // Do not allow a slash “inside” atext.\n    // The reference code is a bit weird, but that’s what it results in.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L307>.\n    // Other than slash, every preceding character is allowed.\n    return !(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.slash || gfmAtext(code));\n}\n/**\n * @param {Code} code\n * @returns {boolean}\n */ function gfmAtext(code) {\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code);\n}\n/**\n * @param {Array<Event>} events\n * @returns {boolean}\n */ function previousUnbalanced(events) {\n    let index = events.length;\n    let result = false;\n    while(index--){\n        const token = events[index][1];\n        if ((token.type === \"labelLink\" || token.type === \"labelImage\") && !token._balanced) {\n            result = true;\n            break;\n        }\n        // If we’ve seen this token, and it was marked as not having any unbalanced\n        // bracket before it, we can exit.\n        if (token._gfmAutolinkLiteralWalkedInto) {\n            result = false;\n            break;\n        }\n    }\n    if (events.length > 0 && !result) {\n        // Mark the last token as “walked into” w/o finding\n        // anything.\n        events[events.length - 1][1]._gfmAutolinkLiteralWalkedInto = true;\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js\n");

/***/ })

};
;