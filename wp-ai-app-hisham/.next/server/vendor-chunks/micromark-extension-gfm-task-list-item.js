"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-task-list-item";
exports.ids = ["vendor-chunks/micromark-extension-gfm-task-list-item"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItemHtml: () => (/* binding */ gfmTaskListItemHtml)\n/* harmony export */ });\n/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */ /**\n * Create an HTML extension for `micromark` to support GFM task list items when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM task list items when serializing to HTML.\n */ function gfmTaskListItemHtml() {\n    return {\n        enter: {\n            taskListCheck () {\n                this.tag('<input type=\"checkbox\" disabled=\"\" ');\n            }\n        },\n        exit: {\n            taskListCheck () {\n                this.tag(\"/>\");\n            },\n            taskListCheckValueChecked () {\n                this.tag('checked=\"\" ');\n            }\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tdGFzay1saXN0LWl0ZW0vZGV2L2xpYi9odG1sLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUVEOzs7Ozs7O0NBT0MsR0FDTSxTQUFTQTtJQUNkLE9BQU87UUFDTEMsT0FBTztZQUNMQztnQkFDRSxJQUFJLENBQUNDLEdBQUcsQ0FBQztZQUNYO1FBQ0Y7UUFDQUMsTUFBTTtZQUNKRjtnQkFDRSxJQUFJLENBQUNDLEdBQUcsQ0FBQztZQUNYO1lBQ0FFO2dCQUNFLElBQUksQ0FBQ0YsR0FBRyxDQUFDO1lBQ1g7UUFDRjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay1leHRlbnNpb24tZ2ZtLXRhc2stbGlzdC1pdGVtL2Rldi9saWIvaHRtbC5qcz83ZTY1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SHRtbEV4dGVuc2lvbn0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuLyoqXG4gKiBDcmVhdGUgYW4gSFRNTCBleHRlbnNpb24gZm9yIGBtaWNyb21hcmtgIHRvIHN1cHBvcnQgR0ZNIHRhc2sgbGlzdCBpdGVtcyB3aGVuXG4gKiBzZXJpYWxpemluZyB0byBIVE1MLlxuICpcbiAqIEByZXR1cm5zIHtIdG1sRXh0ZW5zaW9ufVxuICogICBFeHRlbnNpb24gZm9yIGBtaWNyb21hcmtgIHRoYXQgY2FuIGJlIHBhc3NlZCBpbiBgaHRtbEV4dGVuc2lvbnNgIHRvXG4gKiAgIHN1cHBvcnQgR0ZNIHRhc2sgbGlzdCBpdGVtcyB3aGVuIHNlcmlhbGl6aW5nIHRvIEhUTUwuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZm1UYXNrTGlzdEl0ZW1IdG1sKCkge1xuICByZXR1cm4ge1xuICAgIGVudGVyOiB7XG4gICAgICB0YXNrTGlzdENoZWNrKCkge1xuICAgICAgICB0aGlzLnRhZygnPGlucHV0IHR5cGU9XCJjaGVja2JveFwiIGRpc2FibGVkPVwiXCIgJylcbiAgICAgIH1cbiAgICB9LFxuICAgIGV4aXQ6IHtcbiAgICAgIHRhc2tMaXN0Q2hlY2soKSB7XG4gICAgICAgIHRoaXMudGFnKCcvPicpXG4gICAgICB9LFxuICAgICAgdGFza0xpc3RDaGVja1ZhbHVlQ2hlY2tlZCgpIHtcbiAgICAgICAgdGhpcy50YWcoJ2NoZWNrZWQ9XCJcIiAnKVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbImdmbVRhc2tMaXN0SXRlbUh0bWwiLCJlbnRlciIsInRhc2tMaXN0Q2hlY2siLCJ0YWciLCJleGl0IiwidGFza0xpc3RDaGVja1ZhbHVlQ2hlY2tlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItem: () => (/* binding */ gfmTaskListItem)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/**\n * @import {Extension, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */ \n\n\n\nconst tasklistCheck = {\n    name: \"tasklistCheck\",\n    tokenize: tokenizeTasklistCheck\n};\n/**\n * Create an HTML extension for `micromark` to support GFM task list items\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM task list items when serializing to HTML.\n */ function gfmTaskListItem() {\n    return {\n        text: {\n            [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: tasklistCheck\n        }\n    };\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeTasklistCheck(effects, ok, nok) {\n    const self = this;\n    return open;\n    /**\n   * At start of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function open(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, \"expected `[`\");\n        if (// Exit if there’s stuff before.\n        self.previous !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || // Exit if not in the first content that is the first child of a list\n        // item.\n        !self._gfmTasklistFirstContentOfListItem) {\n            return nok(code);\n        }\n        effects.enter(\"taskListCheck\");\n        effects.enter(\"taskListCheckMarker\");\n        effects.consume(code);\n        effects.exit(\"taskListCheckMarker\");\n        return inside;\n    }\n    /**\n   * In task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */ function inside(code) {\n        // Currently we match how GH works in files.\n        // To match how GH works in comments, use `markdownSpace` (`[\\t ]`) instead\n        // of `markdownLineEndingOrSpace` (`[\\t\\n\\r ]`).\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n            effects.enter(\"taskListCheckValueUnchecked\");\n            effects.consume(code);\n            effects.exit(\"taskListCheckValueUnchecked\");\n            return close;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseX || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseX) {\n            effects.enter(\"taskListCheckValueChecked\");\n            effects.consume(code);\n            effects.exit(\"taskListCheckValueChecked\");\n            return close;\n        }\n        return nok(code);\n    }\n    /**\n   * At close of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function close(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n            effects.enter(\"taskListCheckMarker\");\n            effects.consume(code);\n            effects.exit(\"taskListCheckMarker\");\n            effects.exit(\"taskListCheck\");\n            return after;\n        }\n        return nok(code);\n    }\n    /**\n   * @type {State}\n   */ function after(code) {\n        // EOL in paragraph means there must be something else after it.\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            return ok(code);\n        }\n        // Space or tab?\n        // Check what comes after.\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            return effects.check({\n                tokenize: spaceThenNonSpace\n            }, ok, nok)(code);\n        }\n        // EOF, or non-whitespace, both wrong.\n        return nok(code);\n    }\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function spaceThenNonSpace(effects, ok, nok) {\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, after, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.whitespace);\n    /**\n   * After whitespace, after task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */ function after(code) {\n        // EOF means there was nothing, so bad.\n        // EOL means there’s content after it, so good.\n        // Impossible to have more spaces.\n        // Anything else is good.\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ? nok(code) : ok(code);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js\n");

/***/ })

};
;