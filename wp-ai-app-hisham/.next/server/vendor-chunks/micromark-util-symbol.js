"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-symbol";
exports.ids = ["vendor-chunks/micromark-util-symbol"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/codes.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codes: () => (/* binding */ codes)\n/* harmony export */ });\n/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */ const codes = /** @type {const} */ {\n    carriageReturn: -5,\n    lineFeed: -4,\n    carriageReturnLineFeed: -3,\n    horizontalTab: -2,\n    virtualSpace: -1,\n    eof: null,\n    nul: 0,\n    soh: 1,\n    stx: 2,\n    etx: 3,\n    eot: 4,\n    enq: 5,\n    ack: 6,\n    bel: 7,\n    bs: 8,\n    ht: 9,\n    lf: 10,\n    vt: 11,\n    ff: 12,\n    cr: 13,\n    so: 14,\n    si: 15,\n    dle: 16,\n    dc1: 17,\n    dc2: 18,\n    dc3: 19,\n    dc4: 20,\n    nak: 21,\n    syn: 22,\n    etb: 23,\n    can: 24,\n    em: 25,\n    sub: 26,\n    esc: 27,\n    fs: 28,\n    gs: 29,\n    rs: 30,\n    us: 31,\n    space: 32,\n    exclamationMark: 33,\n    quotationMark: 34,\n    numberSign: 35,\n    dollarSign: 36,\n    percentSign: 37,\n    ampersand: 38,\n    apostrophe: 39,\n    leftParenthesis: 40,\n    rightParenthesis: 41,\n    asterisk: 42,\n    plusSign: 43,\n    comma: 44,\n    dash: 45,\n    dot: 46,\n    slash: 47,\n    digit0: 48,\n    digit1: 49,\n    digit2: 50,\n    digit3: 51,\n    digit4: 52,\n    digit5: 53,\n    digit6: 54,\n    digit7: 55,\n    digit8: 56,\n    digit9: 57,\n    colon: 58,\n    semicolon: 59,\n    lessThan: 60,\n    equalsTo: 61,\n    greaterThan: 62,\n    questionMark: 63,\n    atSign: 64,\n    uppercaseA: 65,\n    uppercaseB: 66,\n    uppercaseC: 67,\n    uppercaseD: 68,\n    uppercaseE: 69,\n    uppercaseF: 70,\n    uppercaseG: 71,\n    uppercaseH: 72,\n    uppercaseI: 73,\n    uppercaseJ: 74,\n    uppercaseK: 75,\n    uppercaseL: 76,\n    uppercaseM: 77,\n    uppercaseN: 78,\n    uppercaseO: 79,\n    uppercaseP: 80,\n    uppercaseQ: 81,\n    uppercaseR: 82,\n    uppercaseS: 83,\n    uppercaseT: 84,\n    uppercaseU: 85,\n    uppercaseV: 86,\n    uppercaseW: 87,\n    uppercaseX: 88,\n    uppercaseY: 89,\n    uppercaseZ: 90,\n    leftSquareBracket: 91,\n    backslash: 92,\n    rightSquareBracket: 93,\n    caret: 94,\n    underscore: 95,\n    graveAccent: 96,\n    lowercaseA: 97,\n    lowercaseB: 98,\n    lowercaseC: 99,\n    lowercaseD: 100,\n    lowercaseE: 101,\n    lowercaseF: 102,\n    lowercaseG: 103,\n    lowercaseH: 104,\n    lowercaseI: 105,\n    lowercaseJ: 106,\n    lowercaseK: 107,\n    lowercaseL: 108,\n    lowercaseM: 109,\n    lowercaseN: 110,\n    lowercaseO: 111,\n    lowercaseP: 112,\n    lowercaseQ: 113,\n    lowercaseR: 114,\n    lowercaseS: 115,\n    lowercaseT: 116,\n    lowercaseU: 117,\n    lowercaseV: 118,\n    lowercaseW: 119,\n    lowercaseX: 120,\n    lowercaseY: 121,\n    lowercaseZ: 122,\n    leftCurlyBrace: 123,\n    verticalBar: 124,\n    rightCurlyBrace: 125,\n    tilde: 126,\n    del: 127,\n    // Unicode Specials block.\n    byteOrderMarker: 65279,\n    // Unicode Specials block.\n    replacementCharacter: 65533 // `�`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constants: () => (/* binding */ constants)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */ const constants = /** @type {const} */ {\n    attentionSideAfter: 2,\n    attentionSideBefore: 1,\n    atxHeadingOpeningFenceSizeMax: 6,\n    autolinkDomainSizeMax: 63,\n    autolinkSchemeSizeMax: 32,\n    cdataOpeningString: \"CDATA[\",\n    characterGroupPunctuation: 2,\n    characterGroupWhitespace: 1,\n    characterReferenceDecimalSizeMax: 7,\n    characterReferenceHexadecimalSizeMax: 6,\n    characterReferenceNamedSizeMax: 31,\n    codeFencedSequenceSizeMin: 3,\n    contentTypeContent: \"content\",\n    contentTypeDocument: \"document\",\n    contentTypeFlow: \"flow\",\n    contentTypeString: \"string\",\n    contentTypeText: \"text\",\n    hardBreakPrefixSizeMin: 2,\n    htmlBasic: 6,\n    htmlCdata: 5,\n    htmlComment: 2,\n    htmlComplete: 7,\n    htmlDeclaration: 4,\n    htmlInstruction: 3,\n    htmlRawSizeMax: 8,\n    htmlRaw: 1,\n    linkResourceDestinationBalanceMax: 32,\n    linkReferenceSizeMax: 999,\n    listItemValueSizeMax: 10,\n    numericBaseDecimal: 10,\n    numericBaseHexadecimal: 0x10,\n    tabSize: 4,\n    thematicBreakMarkerCountMin: 3,\n    v8MaxSafeChunkSize: 10000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/types.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */ // Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nconst types = /** @type {const} */ {\n    // Generic type for data, such as in a title, a destination, etc.\n    data: \"data\",\n    // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n    // Such as, between a fenced code fence and an info string.\n    whitespace: \"whitespace\",\n    // Generic type for line endings (line feed, carriage return, carriage return +\n    // line feed).\n    lineEnding: \"lineEnding\",\n    // A line ending, but ending a blank line.\n    lineEndingBlank: \"lineEndingBlank\",\n    // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n    // line.\n    linePrefix: \"linePrefix\",\n    // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n    // line.\n    lineSuffix: \"lineSuffix\",\n    // Whole ATX heading:\n    //\n    // ```markdown\n    // #\n    // ## Alpha\n    // ### Bravo ###\n    // ```\n    //\n    // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n    atxHeading: \"atxHeading\",\n    // Sequence of number signs in an ATX heading (`###`).\n    atxHeadingSequence: \"atxHeadingSequence\",\n    // Content in an ATX heading (`alpha`).\n    // Includes text.\n    atxHeadingText: \"atxHeadingText\",\n    // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n    // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n    autolink: \"autolink\",\n    // Email autolink w/o markers (`<EMAIL>`)\n    autolinkEmail: \"autolinkEmail\",\n    // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n    autolinkMarker: \"autolinkMarker\",\n    // Protocol autolink w/o markers (`https://example.com`)\n    autolinkProtocol: \"autolinkProtocol\",\n    // A whole character escape (`\\-`).\n    // Includes `escapeMarker` and `characterEscapeValue`.\n    characterEscape: \"characterEscape\",\n    // The escaped character (`-`).\n    characterEscapeValue: \"characterEscapeValue\",\n    // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n    // Includes `characterReferenceMarker`, an optional\n    // `characterReferenceMarkerNumeric`, in which case an optional\n    // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n    characterReference: \"characterReference\",\n    // The start or end marker (`&` or `;`).\n    characterReferenceMarker: \"characterReferenceMarker\",\n    // Mark reference as numeric (`#`).\n    characterReferenceMarkerNumeric: \"characterReferenceMarkerNumeric\",\n    // Mark reference as numeric (`x` or `X`).\n    characterReferenceMarkerHexadecimal: \"characterReferenceMarkerHexadecimal\",\n    // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n    characterReferenceValue: \"characterReferenceValue\",\n    // Whole fenced code:\n    //\n    // ````markdown\n    // ```js\n    // alert(1)\n    // ```\n    // ````\n    codeFenced: \"codeFenced\",\n    // A fenced code fence, including whitespace, sequence, info, and meta\n    // (` ```js `).\n    codeFencedFence: \"codeFencedFence\",\n    // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n    codeFencedFenceSequence: \"codeFencedFenceSequence\",\n    // Info word (`js`) in a fence.\n    // Includes string.\n    codeFencedFenceInfo: \"codeFencedFenceInfo\",\n    // Meta words (`highlight=\"1\"`) in a fence.\n    // Includes string.\n    codeFencedFenceMeta: \"codeFencedFenceMeta\",\n    // A line of code.\n    codeFlowValue: \"codeFlowValue\",\n    // Whole indented code:\n    //\n    // ```markdown\n    //     alert(1)\n    // ```\n    //\n    // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n    codeIndented: \"codeIndented\",\n    // A text code (``` `alpha` ```).\n    // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n    // `codeTextPadding`.\n    codeText: \"codeText\",\n    codeTextData: \"codeTextData\",\n    // A space or line ending right after or before a tick.\n    codeTextPadding: \"codeTextPadding\",\n    // A text code fence (` `` `).\n    codeTextSequence: \"codeTextSequence\",\n    // Whole content:\n    //\n    // ```markdown\n    // [a]: b\n    // c\n    // =\n    // d\n    // ```\n    //\n    // Includes `paragraph` and `definition`.\n    content: \"content\",\n    // Whole definition:\n    //\n    // ```markdown\n    // [micromark]: https://github.com/micromark/micromark\n    // ```\n    //\n    // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n    // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n    definition: \"definition\",\n    // Destination of a definition (`https://github.com/micromark/micromark` or\n    // `<https://github.com/micromark/micromark>`).\n    // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n    definitionDestination: \"definitionDestination\",\n    // Enclosed destination of a definition\n    // (`<https://github.com/micromark/micromark>`).\n    // Includes `definitionDestinationLiteralMarker` and optionally\n    // `definitionDestinationString`.\n    definitionDestinationLiteral: \"definitionDestinationLiteral\",\n    // Markers of an enclosed definition destination (`<` or `>`).\n    definitionDestinationLiteralMarker: \"definitionDestinationLiteralMarker\",\n    // Unenclosed destination of a definition\n    // (`https://github.com/micromark/micromark`).\n    // Includes `definitionDestinationString`.\n    definitionDestinationRaw: \"definitionDestinationRaw\",\n    // Text in an destination (`https://github.com/micromark/micromark`).\n    // Includes string.\n    definitionDestinationString: \"definitionDestinationString\",\n    // Label of a definition (`[micromark]`).\n    // Includes `definitionLabelMarker` and `definitionLabelString`.\n    definitionLabel: \"definitionLabel\",\n    // Markers of a definition label (`[` or `]`).\n    definitionLabelMarker: \"definitionLabelMarker\",\n    // Value of a definition label (`micromark`).\n    // Includes string.\n    definitionLabelString: \"definitionLabelString\",\n    // Marker between a label and a destination (`:`).\n    definitionMarker: \"definitionMarker\",\n    // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n    // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n    definitionTitle: \"definitionTitle\",\n    // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n    definitionTitleMarker: \"definitionTitleMarker\",\n    // Data without markers in a title (`z`).\n    // Includes string.\n    definitionTitleString: \"definitionTitleString\",\n    // Emphasis (`*alpha*`).\n    // Includes `emphasisSequence` and `emphasisText`.\n    emphasis: \"emphasis\",\n    // Sequence of emphasis markers (`*` or `_`).\n    emphasisSequence: \"emphasisSequence\",\n    // Emphasis text (`alpha`).\n    // Includes text.\n    emphasisText: \"emphasisText\",\n    // The character escape marker (`\\`).\n    escapeMarker: \"escapeMarker\",\n    // A hard break created with a backslash (`\\\\n`).\n    // Note: does not include the line ending.\n    hardBreakEscape: \"hardBreakEscape\",\n    // A hard break created with trailing spaces (`  \\n`).\n    // Does not include the line ending.\n    hardBreakTrailing: \"hardBreakTrailing\",\n    // Flow HTML:\n    //\n    // ```markdown\n    // <div\n    // ```\n    //\n    // Inlcudes `lineEnding`, `htmlFlowData`.\n    htmlFlow: \"htmlFlow\",\n    htmlFlowData: \"htmlFlowData\",\n    // HTML in text (the tag in `a <i> b`).\n    // Includes `lineEnding`, `htmlTextData`.\n    htmlText: \"htmlText\",\n    htmlTextData: \"htmlTextData\",\n    // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n    // `![alpha]`).\n    // Includes `label` and an optional `resource` or `reference`.\n    image: \"image\",\n    // Whole link label (`[*alpha*]`).\n    // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n    label: \"label\",\n    // Text in an label (`*alpha*`).\n    // Includes text.\n    labelText: \"labelText\",\n    // Start a link label (`[`).\n    // Includes a `labelMarker`.\n    labelLink: \"labelLink\",\n    // Start an image label (`![`).\n    // Includes `labelImageMarker` and `labelMarker`.\n    labelImage: \"labelImage\",\n    // Marker of a label (`[` or `]`).\n    labelMarker: \"labelMarker\",\n    // Marker to start an image (`!`).\n    labelImageMarker: \"labelImageMarker\",\n    // End a label (`]`).\n    // Includes `labelMarker`.\n    labelEnd: \"labelEnd\",\n    // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n    // Includes `label` and an optional `resource` or `reference`.\n    link: \"link\",\n    // Whole paragraph:\n    //\n    // ```markdown\n    // alpha\n    // bravo.\n    // ```\n    //\n    // Includes text.\n    paragraph: \"paragraph\",\n    // A reference (`[alpha]` or `[]`).\n    // Includes `referenceMarker` and an optional `referenceString`.\n    reference: \"reference\",\n    // A reference marker (`[` or `]`).\n    referenceMarker: \"referenceMarker\",\n    // Reference text (`alpha`).\n    // Includes string.\n    referenceString: \"referenceString\",\n    // A resource (`(https://example.com \"alpha\")`).\n    // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n    // `whitespace` and `resourceTitle`.\n    resource: \"resource\",\n    // A resource destination (`https://example.com`).\n    // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n    resourceDestination: \"resourceDestination\",\n    // A literal resource destination (`<https://example.com>`).\n    // Includes `resourceDestinationLiteralMarker` and optionally\n    // `resourceDestinationString`.\n    resourceDestinationLiteral: \"resourceDestinationLiteral\",\n    // A resource destination marker (`<` or `>`).\n    resourceDestinationLiteralMarker: \"resourceDestinationLiteralMarker\",\n    // A raw resource destination (`https://example.com`).\n    // Includes `resourceDestinationString`.\n    resourceDestinationRaw: \"resourceDestinationRaw\",\n    // Resource destination text (`https://example.com`).\n    // Includes string.\n    resourceDestinationString: \"resourceDestinationString\",\n    // A resource marker (`(` or `)`).\n    resourceMarker: \"resourceMarker\",\n    // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n    // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n    resourceTitle: \"resourceTitle\",\n    // A resource title marker (`\"`, `'`, `(`, or `)`).\n    resourceTitleMarker: \"resourceTitleMarker\",\n    // Resource destination title (`alpha`).\n    // Includes string.\n    resourceTitleString: \"resourceTitleString\",\n    // Whole setext heading:\n    //\n    // ```markdown\n    // alpha\n    // bravo\n    // =====\n    // ```\n    //\n    // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n    // `setextHeadingLine`.\n    setextHeading: \"setextHeading\",\n    // Content in a setext heading (`alpha\\nbravo`).\n    // Includes text.\n    setextHeadingText: \"setextHeadingText\",\n    // Underline in a setext heading, including whitespace suffix (`==`).\n    // Includes `setextHeadingLineSequence`.\n    setextHeadingLine: \"setextHeadingLine\",\n    // Sequence of equals or dash characters in underline in a setext heading (`-`).\n    setextHeadingLineSequence: \"setextHeadingLineSequence\",\n    // Strong (`**alpha**`).\n    // Includes `strongSequence` and `strongText`.\n    strong: \"strong\",\n    // Sequence of strong markers (`**` or `__`).\n    strongSequence: \"strongSequence\",\n    // Strong text (`alpha`).\n    // Includes text.\n    strongText: \"strongText\",\n    // Whole thematic break:\n    //\n    // ```markdown\n    // * * *\n    // ```\n    //\n    // Includes `thematicBreakSequence` and `whitespace`.\n    thematicBreak: \"thematicBreak\",\n    // A sequence of one or more thematic break markers (`***`).\n    thematicBreakSequence: \"thematicBreakSequence\",\n    // Whole block quote:\n    //\n    // ```markdown\n    // > a\n    // >\n    // > b\n    // ```\n    //\n    // Includes `blockQuotePrefix` and flow.\n    blockQuote: \"blockQuote\",\n    // The `>` or `> ` of a block quote.\n    blockQuotePrefix: \"blockQuotePrefix\",\n    // The `>` of a block quote prefix.\n    blockQuoteMarker: \"blockQuoteMarker\",\n    // The optional ` ` of a block quote prefix.\n    blockQuotePrefixWhitespace: \"blockQuotePrefixWhitespace\",\n    // Whole ordered list:\n    //\n    // ```markdown\n    // 1. a\n    //    b\n    // ```\n    //\n    // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n    // lines.\n    listOrdered: \"listOrdered\",\n    // Whole unordered list:\n    //\n    // ```markdown\n    // - a\n    //   b\n    // ```\n    //\n    // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n    // lines.\n    listUnordered: \"listUnordered\",\n    // The indent of further list item lines.\n    listItemIndent: \"listItemIndent\",\n    // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n    listItemMarker: \"listItemMarker\",\n    // The thing that starts a list item, such as `1. `.\n    // Includes `listItemValue` if ordered, `listItemMarker`, and\n    // `listItemPrefixWhitespace` (unless followed by a line ending).\n    listItemPrefix: \"listItemPrefix\",\n    // The whitespace after a marker.\n    listItemPrefixWhitespace: \"listItemPrefixWhitespace\",\n    // The numerical value of an ordered item.\n    listItemValue: \"listItemValue\",\n    // Internal types used for subtokenizers, compiled away\n    chunkDocument: \"chunkDocument\",\n    chunkContent: \"chunkContent\",\n    chunkFlow: \"chunkFlow\",\n    chunkText: \"chunkText\",\n    chunkString: \"chunkString\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/values.js":
/*!**********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/values.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   values: () => (/* binding */ values)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */ const values = /** @type {const} */ {\n    ht: \"\t\",\n    lf: \"\\n\",\n    cr: \"\\r\",\n    space: \" \",\n    exclamationMark: \"!\",\n    quotationMark: '\"',\n    numberSign: \"#\",\n    dollarSign: \"$\",\n    percentSign: \"%\",\n    ampersand: \"&\",\n    apostrophe: \"'\",\n    leftParenthesis: \"(\",\n    rightParenthesis: \")\",\n    asterisk: \"*\",\n    plusSign: \"+\",\n    comma: \",\",\n    dash: \"-\",\n    dot: \".\",\n    slash: \"/\",\n    digit0: \"0\",\n    digit1: \"1\",\n    digit2: \"2\",\n    digit3: \"3\",\n    digit4: \"4\",\n    digit5: \"5\",\n    digit6: \"6\",\n    digit7: \"7\",\n    digit8: \"8\",\n    digit9: \"9\",\n    colon: \":\",\n    semicolon: \";\",\n    lessThan: \"<\",\n    equalsTo: \"=\",\n    greaterThan: \">\",\n    questionMark: \"?\",\n    atSign: \"@\",\n    uppercaseA: \"A\",\n    uppercaseB: \"B\",\n    uppercaseC: \"C\",\n    uppercaseD: \"D\",\n    uppercaseE: \"E\",\n    uppercaseF: \"F\",\n    uppercaseG: \"G\",\n    uppercaseH: \"H\",\n    uppercaseI: \"I\",\n    uppercaseJ: \"J\",\n    uppercaseK: \"K\",\n    uppercaseL: \"L\",\n    uppercaseM: \"M\",\n    uppercaseN: \"N\",\n    uppercaseO: \"O\",\n    uppercaseP: \"P\",\n    uppercaseQ: \"Q\",\n    uppercaseR: \"R\",\n    uppercaseS: \"S\",\n    uppercaseT: \"T\",\n    uppercaseU: \"U\",\n    uppercaseV: \"V\",\n    uppercaseW: \"W\",\n    uppercaseX: \"X\",\n    uppercaseY: \"Y\",\n    uppercaseZ: \"Z\",\n    leftSquareBracket: \"[\",\n    backslash: \"\\\\\",\n    rightSquareBracket: \"]\",\n    caret: \"^\",\n    underscore: \"_\",\n    graveAccent: \"`\",\n    lowercaseA: \"a\",\n    lowercaseB: \"b\",\n    lowercaseC: \"c\",\n    lowercaseD: \"d\",\n    lowercaseE: \"e\",\n    lowercaseF: \"f\",\n    lowercaseG: \"g\",\n    lowercaseH: \"h\",\n    lowercaseI: \"i\",\n    lowercaseJ: \"j\",\n    lowercaseK: \"k\",\n    lowercaseL: \"l\",\n    lowercaseM: \"m\",\n    lowercaseN: \"n\",\n    lowercaseO: \"o\",\n    lowercaseP: \"p\",\n    lowercaseQ: \"q\",\n    lowercaseR: \"r\",\n    lowercaseS: \"s\",\n    lowercaseT: \"t\",\n    lowercaseU: \"u\",\n    lowercaseV: \"v\",\n    lowercaseW: \"w\",\n    lowercaseX: \"x\",\n    lowercaseY: \"y\",\n    lowercaseZ: \"z\",\n    leftCurlyBrace: \"{\",\n    verticalBar: \"|\",\n    rightCurlyBrace: \"}\",\n    tilde: \"~\",\n    replacementCharacter: \"�\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/values.js\n");

/***/ })

};
;