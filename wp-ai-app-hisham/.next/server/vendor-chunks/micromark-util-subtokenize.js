"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-subtokenize";
exports.ids = ["vendor-chunks/micromark-util-subtokenize"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-util-subtokenize/dev/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark-util-subtokenize/dev/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* reexport safe */ _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer),\n/* harmony export */   subtokenize: () => (/* binding */ subtokenize)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/splice-buffer.js */ \"(ssr)/./node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\");\n/**\n * @import {Chunk, Event, Token} from 'micromark-util-types'\n */ \n\n\n\n// Hidden API exposed for testing.\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} eventsArray\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */ // eslint-disable-next-line complexity\nfunction subtokenize(eventsArray) {\n    /** @type {Record<string, number>} */ const jumps = {};\n    let index = -1;\n    /** @type {Event} */ let event;\n    /** @type {number | undefined} */ let lineIndex;\n    /** @type {number} */ let otherIndex;\n    /** @type {Event} */ let otherEvent;\n    /** @type {Array<Event>} */ let parameters;\n    /** @type {Array<Event>} */ let subevents;\n    /** @type {boolean | undefined} */ let more;\n    const events = new _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer(eventsArray);\n    while(++index < events.length){\n        while(index in jumps){\n            index = jumps[index];\n        }\n        event = events.get(index);\n        // Add a hook for the GFM tasklist extension, which needs to know if text\n        // is in the first content of a list item.\n        if (index && event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow && events.get(index - 1)[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefix) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(event[1]._tokenizer, \"expected `_tokenizer` on subtokens\");\n            subevents = event[1]._tokenizer.events;\n            otherIndex = 0;\n            if (otherIndex < subevents.length && subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank) {\n                otherIndex += 2;\n            }\n            if (otherIndex < subevents.length && subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content) {\n                while(++otherIndex < subevents.length){\n                    if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content) {\n                        break;\n                    }\n                    if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkText) {\n                        subevents[otherIndex][1]._isInFirstContentOfListItem = true;\n                        otherIndex++;\n                    }\n                }\n            }\n        }\n        // Enter.\n        if (event[0] === \"enter\") {\n            if (event[1].contentType) {\n                Object.assign(jumps, subcontent(events, index));\n                index = jumps[index];\n                more = true;\n            }\n        } else if (event[1]._container) {\n            otherIndex = index;\n            lineIndex = undefined;\n            while(otherIndex--){\n                otherEvent = events.get(otherIndex);\n                if (otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding || otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank) {\n                    if (otherEvent[0] === \"enter\") {\n                        if (lineIndex) {\n                            events.get(lineIndex)[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank;\n                        }\n                        otherEvent[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding;\n                        lineIndex = otherIndex;\n                    }\n                } else if (otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix || otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.listItemIndent) {\n                // Move past.\n                } else {\n                    break;\n                }\n            }\n            if (lineIndex) {\n                // Fix position.\n                event[1].end = {\n                    ...events.get(lineIndex)[1].start\n                };\n                // Switch container exit w/ line endings.\n                parameters = events.slice(lineIndex, index);\n                parameters.unshift(event);\n                events.splice(lineIndex, index - lineIndex + 1, parameters);\n            }\n        }\n    }\n    // The changes to the `events` buffer must be copied back into the eventsArray\n    (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__.splice)(eventsArray, 0, Number.POSITIVE_INFINITY, events.slice(0));\n    return !more;\n}\n/**\n * Tokenize embedded tokens.\n *\n * @param {SpliceBuffer<Event>} events\n *   Events.\n * @param {number} eventIndex\n *   Index.\n * @returns {Record<string, number>}\n *   Gaps.\n */ function subcontent(events, eventIndex) {\n    const token = events.get(eventIndex)[1];\n    const context = events.get(eventIndex)[2];\n    let startPosition = eventIndex - 1;\n    /** @type {Array<number>} */ const startPositions = [];\n    (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(token.contentType, \"expected `contentType` on subtokens\");\n    let tokenizer = token._tokenizer;\n    if (!tokenizer) {\n        tokenizer = context.parser[token.contentType](token.start);\n        if (token._contentTypeTextTrailing) {\n            tokenizer._contentTypeTextTrailing = true;\n        }\n    }\n    const childEvents = tokenizer.events;\n    /** @type {Array<[number, number]>} */ const jumps = [];\n    /** @type {Record<string, number>} */ const gaps = {};\n    /** @type {Array<Chunk>} */ let stream;\n    /** @type {Token | undefined} */ let previous;\n    let index = -1;\n    /** @type {Token | undefined} */ let current = token;\n    let adjust = 0;\n    let start = 0;\n    const breaks = [\n        start\n    ];\n    // Loop forward through the linked tokens to pass them in order to the\n    // subtokenizer.\n    while(current){\n        // Find the position of the event for this token.\n        while(events.get(++startPosition)[1] !== current){\n        // Empty.\n        }\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!previous || current.previous === previous, \"expected previous to match\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!previous || previous.next === current, \"expected next to match\");\n        startPositions.push(startPosition);\n        if (!current._tokenizer) {\n            stream = context.sliceStream(current);\n            if (!current.next) {\n                stream.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof);\n            }\n            if (previous) {\n                tokenizer.defineSkip(current.start);\n            }\n            if (current._isInFirstContentOfListItem) {\n                tokenizer._gfmTasklistFirstContentOfListItem = true;\n            }\n            tokenizer.write(stream);\n            if (current._isInFirstContentOfListItem) {\n                tokenizer._gfmTasklistFirstContentOfListItem = undefined;\n            }\n        }\n        // Unravel the next token.\n        previous = current;\n        current = current.next;\n    }\n    // Now, loop back through all events (and linked tokens), to figure out which\n    // parts belong where.\n    current = token;\n    while(++index < childEvents.length){\n        if (// Find a void token that includes a break.\n        childEvents[index][0] === \"exit\" && childEvents[index - 1][0] === \"enter\" && childEvents[index][1].type === childEvents[index - 1][1].type && childEvents[index][1].start.line !== childEvents[index][1].end.line) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(current, \"expected a current token\");\n            start = index + 1;\n            breaks.push(start);\n            // Help GC.\n            current._tokenizer = undefined;\n            current.previous = undefined;\n            current = current.next;\n        }\n    }\n    // Help GC.\n    tokenizer.events = [];\n    // If there’s one more token (which is the cases for lines that end in an\n    // EOF), that’s perfect: the last point we found starts it.\n    // If there isn’t then make sure any remaining content is added to it.\n    if (current) {\n        // Help GC.\n        current._tokenizer = undefined;\n        current.previous = undefined;\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!current.next, \"expected no next token\");\n    } else {\n        breaks.pop();\n    }\n    // Now splice the events from the subtokenizer into the current events,\n    // moving back to front so that splice indices aren’t affected.\n    index = breaks.length;\n    while(index--){\n        const slice = childEvents.slice(breaks[index], breaks[index + 1]);\n        const start = startPositions.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(start !== undefined, \"expected a start position when splicing\");\n        jumps.push([\n            start,\n            start + slice.length - 1\n        ]);\n        events.splice(start, 2, slice);\n    }\n    jumps.reverse();\n    index = -1;\n    while(++index < jumps.length){\n        gaps[adjust + jumps[index][0]] = adjust + jumps[index][1];\n        adjust += jumps[index][1] - jumps[index][0] - 1;\n    }\n    return gaps;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-subtokenize/dev/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js":
/*!**************************************************************************!*\
  !*** ./node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* binding */ SpliceBuffer)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n\n/**\n * Some of the internal operations of micromark do lots of editing\n * operations on very large arrays. This runs into problems with two\n * properties of most circa-2020 JavaScript interpreters:\n *\n *  - Array-length modifications at the high end of an array (push/pop) are\n *    expected to be common and are implemented in (amortized) time\n *    proportional to the number of elements added or removed, whereas\n *    other operations (shift/unshift and splice) are much less efficient.\n *  - Function arguments are passed on the stack, so adding tens of thousands\n *    of elements to an array with `arr.push(...newElements)` will frequently\n *    cause stack overflows. (see <https://stackoverflow.com/questions/22123769/rangeerror-maximum-call-stack-size-exceeded-why>)\n *\n * SpliceBuffers are an implementation of gap buffers, which are a\n * generalization of the \"queue made of two stacks\" idea. The splice buffer\n * maintains a cursor, and moving the cursor has cost proportional to the\n * distance the cursor moves, but inserting, deleting, or splicing in\n * new information at the cursor is as efficient as the push/pop operation.\n * This allows for an efficient sequence of splices (or pushes, pops, shifts,\n * or unshifts) as long such edits happen at the same part of the array or\n * generally sweep through the array from the beginning to the end.\n *\n * The interface for splice buffers also supports large numbers of inputs by\n * passing a single array argument rather passing multiple arguments on the\n * function call stack.\n *\n * @template T\n *   Item type.\n */ class SpliceBuffer {\n    /**\n   * @param {ReadonlyArray<T> | null | undefined} [initial]\n   *   Initial items (optional).\n   * @returns\n   *   Splice buffer.\n   */ constructor(initial){\n        /** @type {Array<T>} */ this.left = initial ? [\n            ...initial\n        ] : [];\n        /** @type {Array<T>} */ this.right = [];\n    }\n    /**\n   * Array access;\n   * does not move the cursor.\n   *\n   * @param {number} index\n   *   Index.\n   * @return {T}\n   *   Item.\n   */ get(index) {\n        if (index < 0 || index >= this.left.length + this.right.length) {\n            throw new RangeError(\"Cannot access index `\" + index + \"` in a splice buffer of size `\" + (this.left.length + this.right.length) + \"`\");\n        }\n        if (index < this.left.length) return this.left[index];\n        return this.right[this.right.length - index + this.left.length - 1];\n    }\n    /**\n   * The length of the splice buffer, one greater than the largest index in the\n   * array.\n   */ get length() {\n        return this.left.length + this.right.length;\n    }\n    /**\n   * Remove and return `list[0]`;\n   * moves the cursor to `0`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */ shift() {\n        this.setCursor(0);\n        return this.right.pop();\n    }\n    /**\n   * Slice the buffer to get an array;\n   * does not move the cursor.\n   *\n   * @param {number} start\n   *   Start.\n   * @param {number | null | undefined} [end]\n   *   End (optional).\n   * @returns {Array<T>}\n   *   Array of items.\n   */ slice(start, end) {\n        /** @type {number} */ const stop = end === null || end === undefined ? Number.POSITIVE_INFINITY : end;\n        if (stop < this.left.length) {\n            return this.left.slice(start, stop);\n        }\n        if (start > this.left.length) {\n            return this.right.slice(this.right.length - stop + this.left.length, this.right.length - start + this.left.length).reverse();\n        }\n        return this.left.slice(start).concat(this.right.slice(this.right.length - stop + this.left.length).reverse());\n    }\n    /**\n   * Mimics the behavior of Array.prototype.splice() except for the change of\n   * interface necessary to avoid segfaults when patching in very large arrays.\n   *\n   * This operation moves cursor is moved to `start` and results in the cursor\n   * placed after any inserted items.\n   *\n   * @param {number} start\n   *   Start;\n   *   zero-based index at which to start changing the array;\n   *   negative numbers count backwards from the end of the array and values\n   *   that are out-of bounds are clamped to the appropriate end of the array.\n   * @param {number | null | undefined} [deleteCount=0]\n   *   Delete count (default: `0`);\n   *   maximum number of elements to delete, starting from start.\n   * @param {Array<T> | null | undefined} [items=[]]\n   *   Items to include in place of the deleted items (default: `[]`).\n   * @return {Array<T>}\n   *   Any removed items.\n   */ splice(start, deleteCount, items) {\n        /** @type {number} */ const count = deleteCount || 0;\n        this.setCursor(Math.trunc(start));\n        const removed = this.right.splice(this.right.length - count, Number.POSITIVE_INFINITY);\n        if (items) chunkedPush(this.left, items);\n        return removed.reverse();\n    }\n    /**\n   * Remove and return the highest-numbered item in the array, so\n   * `list[list.length - 1]`;\n   * Moves the cursor to `length`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */ pop() {\n        this.setCursor(Number.POSITIVE_INFINITY);\n        return this.left.pop();\n    }\n    /**\n   * Inserts a single item to the high-numbered side of the array;\n   * moves the cursor to `length`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */ push(item) {\n        this.setCursor(Number.POSITIVE_INFINITY);\n        this.left.push(item);\n    }\n    /**\n   * Inserts many items to the high-numbered side of the array.\n   * Moves the cursor to `length`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */ pushMany(items) {\n        this.setCursor(Number.POSITIVE_INFINITY);\n        chunkedPush(this.left, items);\n    }\n    /**\n   * Inserts a single item to the low-numbered side of the array;\n   * Moves the cursor to `0`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */ unshift(item) {\n        this.setCursor(0);\n        this.right.push(item);\n    }\n    /**\n   * Inserts many items to the low-numbered side of the array;\n   * moves the cursor to `0`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */ unshiftMany(items) {\n        this.setCursor(0);\n        chunkedPush(this.right, items.reverse());\n    }\n    /**\n   * Move the cursor to a specific position in the array. Requires\n   * time proportional to the distance moved.\n   *\n   * If `n < 0`, the cursor will end up at the beginning.\n   * If `n > length`, the cursor will end up at the end.\n   *\n   * @param {number} n\n   *   Position.\n   * @return {undefined}\n   *   Nothing.\n   */ setCursor(n) {\n        if (n === this.left.length || n > this.left.length && this.right.length === 0 || n < 0 && this.left.length === 0) return;\n        if (n < this.left.length) {\n            // Move cursor to the this.left\n            const removed = this.left.splice(n, Number.POSITIVE_INFINITY);\n            chunkedPush(this.right, removed.reverse());\n        } else {\n            // Move cursor to the this.right\n            const removed = this.right.splice(this.left.length + this.right.length - n, Number.POSITIVE_INFINITY);\n            chunkedPush(this.left, removed.reverse());\n        }\n    }\n}\n/**\n * Avoid stack overflow by pushing items onto the stack in segments\n *\n * @template T\n *   Item type.\n * @param {Array<T>} list\n *   List to inject into.\n * @param {ReadonlyArray<T>} right\n *   Items to inject.\n * @return {undefined}\n *   Nothing.\n */ function chunkedPush(list, right) {\n    /** @type {number} */ let chunkStart = 0;\n    if (right.length < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize) {\n        list.push(...right);\n    } else {\n        while(chunkStart < right.length){\n            list.push(...right.slice(chunkStart, chunkStart + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize));\n            chunkStart += micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLXV0aWwtc3VidG9rZW5pemUvZGV2L2xpYi9zcGxpY2UtYnVmZmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStDO0FBRS9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBNEJDLEdBQ00sTUFBTUM7SUFDWDs7Ozs7R0FLQyxHQUNEQyxZQUFZQyxPQUFPLENBQUU7UUFDbkIscUJBQXFCLEdBQ3JCLElBQUksQ0FBQ0MsSUFBSSxHQUFHRCxVQUFVO2VBQUlBO1NBQVEsR0FBRyxFQUFFO1FBQ3ZDLHFCQUFxQixHQUNyQixJQUFJLENBQUNFLEtBQUssR0FBRyxFQUFFO0lBQ2pCO0lBRUE7Ozs7Ozs7O0dBUUMsR0FDREMsSUFBSUMsS0FBSyxFQUFFO1FBQ1QsSUFBSUEsUUFBUSxLQUFLQSxTQUFTLElBQUksQ0FBQ0gsSUFBSSxDQUFDSSxNQUFNLEdBQUcsSUFBSSxDQUFDSCxLQUFLLENBQUNHLE1BQU0sRUFBRTtZQUM5RCxNQUFNLElBQUlDLFdBQ1IsMEJBQ0VGLFFBQ0EsbUNBQ0MsS0FBSSxDQUFDSCxJQUFJLENBQUNJLE1BQU0sR0FBRyxJQUFJLENBQUNILEtBQUssQ0FBQ0csTUFBTSxJQUNyQztRQUVOO1FBRUEsSUFBSUQsUUFBUSxJQUFJLENBQUNILElBQUksQ0FBQ0ksTUFBTSxFQUFFLE9BQU8sSUFBSSxDQUFDSixJQUFJLENBQUNHLE1BQU07UUFDckQsT0FBTyxJQUFJLENBQUNGLEtBQUssQ0FBQyxJQUFJLENBQUNBLEtBQUssQ0FBQ0csTUFBTSxHQUFHRCxRQUFRLElBQUksQ0FBQ0gsSUFBSSxDQUFDSSxNQUFNLEdBQUcsRUFBRTtJQUNyRTtJQUVBOzs7R0FHQyxHQUNELElBQUlBLFNBQVM7UUFDWCxPQUFPLElBQUksQ0FBQ0osSUFBSSxDQUFDSSxNQUFNLEdBQUcsSUFBSSxDQUFDSCxLQUFLLENBQUNHLE1BQU07SUFDN0M7SUFFQTs7Ozs7O0dBTUMsR0FDREUsUUFBUTtRQUNOLElBQUksQ0FBQ0MsU0FBUyxDQUFDO1FBQ2YsT0FBTyxJQUFJLENBQUNOLEtBQUssQ0FBQ08sR0FBRztJQUN2QjtJQUVBOzs7Ozs7Ozs7O0dBVUMsR0FDREMsTUFBTUMsS0FBSyxFQUFFQyxHQUFHLEVBQUU7UUFDaEIsbUJBQW1CLEdBQ25CLE1BQU1DLE9BQ0pELFFBQVEsUUFBUUEsUUFBUUUsWUFBWUMsT0FBT0MsaUJBQWlCLEdBQUdKO1FBRWpFLElBQUlDLE9BQU8sSUFBSSxDQUFDWixJQUFJLENBQUNJLE1BQU0sRUFBRTtZQUMzQixPQUFPLElBQUksQ0FBQ0osSUFBSSxDQUFDUyxLQUFLLENBQUNDLE9BQU9FO1FBQ2hDO1FBRUEsSUFBSUYsUUFBUSxJQUFJLENBQUNWLElBQUksQ0FBQ0ksTUFBTSxFQUFFO1lBQzVCLE9BQU8sSUFBSSxDQUFDSCxLQUFLLENBQ2RRLEtBQUssQ0FDSixJQUFJLENBQUNSLEtBQUssQ0FBQ0csTUFBTSxHQUFHUSxPQUFPLElBQUksQ0FBQ1osSUFBSSxDQUFDSSxNQUFNLEVBQzNDLElBQUksQ0FBQ0gsS0FBSyxDQUFDRyxNQUFNLEdBQUdNLFFBQVEsSUFBSSxDQUFDVixJQUFJLENBQUNJLE1BQU0sRUFFN0NZLE9BQU87UUFDWjtRQUVBLE9BQU8sSUFBSSxDQUFDaEIsSUFBSSxDQUNiUyxLQUFLLENBQUNDLE9BQ05PLE1BQU0sQ0FDTCxJQUFJLENBQUNoQixLQUFLLENBQUNRLEtBQUssQ0FBQyxJQUFJLENBQUNSLEtBQUssQ0FBQ0csTUFBTSxHQUFHUSxPQUFPLElBQUksQ0FBQ1osSUFBSSxDQUFDSSxNQUFNLEVBQUVZLE9BQU87SUFFM0U7SUFFQTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztHQW1CQyxHQUNERSxPQUFPUixLQUFLLEVBQUVTLFdBQVcsRUFBRUMsS0FBSyxFQUFFO1FBQ2hDLG1CQUFtQixHQUNuQixNQUFNQyxRQUFRRixlQUFlO1FBRTdCLElBQUksQ0FBQ1osU0FBUyxDQUFDZSxLQUFLQyxLQUFLLENBQUNiO1FBQzFCLE1BQU1jLFVBQVUsSUFBSSxDQUFDdkIsS0FBSyxDQUFDaUIsTUFBTSxDQUMvQixJQUFJLENBQUNqQixLQUFLLENBQUNHLE1BQU0sR0FBR2lCLE9BQ3BCUCxPQUFPQyxpQkFBaUI7UUFFMUIsSUFBSUssT0FBT0ssWUFBWSxJQUFJLENBQUN6QixJQUFJLEVBQUVvQjtRQUNsQyxPQUFPSSxRQUFRUixPQUFPO0lBQ3hCO0lBRUE7Ozs7Ozs7R0FPQyxHQUNEUixNQUFNO1FBQ0osSUFBSSxDQUFDRCxTQUFTLENBQUNPLE9BQU9DLGlCQUFpQjtRQUN2QyxPQUFPLElBQUksQ0FBQ2YsSUFBSSxDQUFDUSxHQUFHO0lBQ3RCO0lBRUE7Ozs7Ozs7O0dBUUMsR0FDRGtCLEtBQUtDLElBQUksRUFBRTtRQUNULElBQUksQ0FBQ3BCLFNBQVMsQ0FBQ08sT0FBT0MsaUJBQWlCO1FBQ3ZDLElBQUksQ0FBQ2YsSUFBSSxDQUFDMEIsSUFBSSxDQUFDQztJQUNqQjtJQUVBOzs7Ozs7OztHQVFDLEdBQ0RDLFNBQVNSLEtBQUssRUFBRTtRQUNkLElBQUksQ0FBQ2IsU0FBUyxDQUFDTyxPQUFPQyxpQkFBaUI7UUFDdkNVLFlBQVksSUFBSSxDQUFDekIsSUFBSSxFQUFFb0I7SUFDekI7SUFFQTs7Ozs7Ozs7R0FRQyxHQUNEUyxRQUFRRixJQUFJLEVBQUU7UUFDWixJQUFJLENBQUNwQixTQUFTLENBQUM7UUFDZixJQUFJLENBQUNOLEtBQUssQ0FBQ3lCLElBQUksQ0FBQ0M7SUFDbEI7SUFFQTs7Ozs7Ozs7R0FRQyxHQUNERyxZQUFZVixLQUFLLEVBQUU7UUFDakIsSUFBSSxDQUFDYixTQUFTLENBQUM7UUFDZmtCLFlBQVksSUFBSSxDQUFDeEIsS0FBSyxFQUFFbUIsTUFBTUosT0FBTztJQUN2QztJQUVBOzs7Ozs7Ozs7OztHQVdDLEdBQ0RULFVBQVV3QixDQUFDLEVBQUU7UUFDWCxJQUNFQSxNQUFNLElBQUksQ0FBQy9CLElBQUksQ0FBQ0ksTUFBTSxJQUNyQjJCLElBQUksSUFBSSxDQUFDL0IsSUFBSSxDQUFDSSxNQUFNLElBQUksSUFBSSxDQUFDSCxLQUFLLENBQUNHLE1BQU0sS0FBSyxLQUM5QzJCLElBQUksS0FBSyxJQUFJLENBQUMvQixJQUFJLENBQUNJLE1BQU0sS0FBSyxHQUUvQjtRQUNGLElBQUkyQixJQUFJLElBQUksQ0FBQy9CLElBQUksQ0FBQ0ksTUFBTSxFQUFFO1lBQ3hCLCtCQUErQjtZQUMvQixNQUFNb0IsVUFBVSxJQUFJLENBQUN4QixJQUFJLENBQUNrQixNQUFNLENBQUNhLEdBQUdqQixPQUFPQyxpQkFBaUI7WUFDNURVLFlBQVksSUFBSSxDQUFDeEIsS0FBSyxFQUFFdUIsUUFBUVIsT0FBTztRQUN6QyxPQUFPO1lBQ0wsZ0NBQWdDO1lBQ2hDLE1BQU1RLFVBQVUsSUFBSSxDQUFDdkIsS0FBSyxDQUFDaUIsTUFBTSxDQUMvQixJQUFJLENBQUNsQixJQUFJLENBQUNJLE1BQU0sR0FBRyxJQUFJLENBQUNILEtBQUssQ0FBQ0csTUFBTSxHQUFHMkIsR0FDdkNqQixPQUFPQyxpQkFBaUI7WUFFMUJVLFlBQVksSUFBSSxDQUFDekIsSUFBSSxFQUFFd0IsUUFBUVIsT0FBTztRQUN4QztJQUNGO0FBQ0Y7QUFFQTs7Ozs7Ozs7Ozs7Q0FXQyxHQUNELFNBQVNTLFlBQVlPLElBQUksRUFBRS9CLEtBQUs7SUFDOUIsbUJBQW1CLEdBQ25CLElBQUlnQyxhQUFhO0lBRWpCLElBQUloQyxNQUFNRyxNQUFNLEdBQUdSLDREQUFTQSxDQUFDc0Msa0JBQWtCLEVBQUU7UUFDL0NGLEtBQUtOLElBQUksSUFBSXpCO0lBQ2YsT0FBTztRQUNMLE1BQU9nQyxhQUFhaEMsTUFBTUcsTUFBTSxDQUFFO1lBQ2hDNEIsS0FBS04sSUFBSSxJQUNKekIsTUFBTVEsS0FBSyxDQUFDd0IsWUFBWUEsYUFBYXJDLDREQUFTQSxDQUFDc0Msa0JBQWtCO1lBRXRFRCxjQUFjckMsNERBQVNBLENBQUNzQyxrQkFBa0I7UUFDNUM7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9taWNyb21hcmstdXRpbC1zdWJ0b2tlbml6ZS9kZXYvbGliL3NwbGljZS1idWZmZXIuanM/YTAyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2NvbnN0YW50c30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sJ1xuXG4vKipcbiAqIFNvbWUgb2YgdGhlIGludGVybmFsIG9wZXJhdGlvbnMgb2YgbWljcm9tYXJrIGRvIGxvdHMgb2YgZWRpdGluZ1xuICogb3BlcmF0aW9ucyBvbiB2ZXJ5IGxhcmdlIGFycmF5cy4gVGhpcyBydW5zIGludG8gcHJvYmxlbXMgd2l0aCB0d29cbiAqIHByb3BlcnRpZXMgb2YgbW9zdCBjaXJjYS0yMDIwIEphdmFTY3JpcHQgaW50ZXJwcmV0ZXJzOlxuICpcbiAqICAtIEFycmF5LWxlbmd0aCBtb2RpZmljYXRpb25zIGF0IHRoZSBoaWdoIGVuZCBvZiBhbiBhcnJheSAocHVzaC9wb3ApIGFyZVxuICogICAgZXhwZWN0ZWQgdG8gYmUgY29tbW9uIGFuZCBhcmUgaW1wbGVtZW50ZWQgaW4gKGFtb3J0aXplZCkgdGltZVxuICogICAgcHJvcG9ydGlvbmFsIHRvIHRoZSBudW1iZXIgb2YgZWxlbWVudHMgYWRkZWQgb3IgcmVtb3ZlZCwgd2hlcmVhc1xuICogICAgb3RoZXIgb3BlcmF0aW9ucyAoc2hpZnQvdW5zaGlmdCBhbmQgc3BsaWNlKSBhcmUgbXVjaCBsZXNzIGVmZmljaWVudC5cbiAqICAtIEZ1bmN0aW9uIGFyZ3VtZW50cyBhcmUgcGFzc2VkIG9uIHRoZSBzdGFjaywgc28gYWRkaW5nIHRlbnMgb2YgdGhvdXNhbmRzXG4gKiAgICBvZiBlbGVtZW50cyB0byBhbiBhcnJheSB3aXRoIGBhcnIucHVzaCguLi5uZXdFbGVtZW50cylgIHdpbGwgZnJlcXVlbnRseVxuICogICAgY2F1c2Ugc3RhY2sgb3ZlcmZsb3dzLiAoc2VlIDxodHRwczovL3N0YWNrb3ZlcmZsb3cuY29tL3F1ZXN0aW9ucy8yMjEyMzc2OS9yYW5nZWVycm9yLW1heGltdW0tY2FsbC1zdGFjay1zaXplLWV4Y2VlZGVkLXdoeT4pXG4gKlxuICogU3BsaWNlQnVmZmVycyBhcmUgYW4gaW1wbGVtZW50YXRpb24gb2YgZ2FwIGJ1ZmZlcnMsIHdoaWNoIGFyZSBhXG4gKiBnZW5lcmFsaXphdGlvbiBvZiB0aGUgXCJxdWV1ZSBtYWRlIG9mIHR3byBzdGFja3NcIiBpZGVhLiBUaGUgc3BsaWNlIGJ1ZmZlclxuICogbWFpbnRhaW5zIGEgY3Vyc29yLCBhbmQgbW92aW5nIHRoZSBjdXJzb3IgaGFzIGNvc3QgcHJvcG9ydGlvbmFsIHRvIHRoZVxuICogZGlzdGFuY2UgdGhlIGN1cnNvciBtb3ZlcywgYnV0IGluc2VydGluZywgZGVsZXRpbmcsIG9yIHNwbGljaW5nIGluXG4gKiBuZXcgaW5mb3JtYXRpb24gYXQgdGhlIGN1cnNvciBpcyBhcyBlZmZpY2llbnQgYXMgdGhlIHB1c2gvcG9wIG9wZXJhdGlvbi5cbiAqIFRoaXMgYWxsb3dzIGZvciBhbiBlZmZpY2llbnQgc2VxdWVuY2Ugb2Ygc3BsaWNlcyAob3IgcHVzaGVzLCBwb3BzLCBzaGlmdHMsXG4gKiBvciB1bnNoaWZ0cykgYXMgbG9uZyBzdWNoIGVkaXRzIGhhcHBlbiBhdCB0aGUgc2FtZSBwYXJ0IG9mIHRoZSBhcnJheSBvclxuICogZ2VuZXJhbGx5IHN3ZWVwIHRocm91Z2ggdGhlIGFycmF5IGZyb20gdGhlIGJlZ2lubmluZyB0byB0aGUgZW5kLlxuICpcbiAqIFRoZSBpbnRlcmZhY2UgZm9yIHNwbGljZSBidWZmZXJzIGFsc28gc3VwcG9ydHMgbGFyZ2UgbnVtYmVycyBvZiBpbnB1dHMgYnlcbiAqIHBhc3NpbmcgYSBzaW5nbGUgYXJyYXkgYXJndW1lbnQgcmF0aGVyIHBhc3NpbmcgbXVsdGlwbGUgYXJndW1lbnRzIG9uIHRoZVxuICogZnVuY3Rpb24gY2FsbCBzdGFjay5cbiAqXG4gKiBAdGVtcGxhdGUgVFxuICogICBJdGVtIHR5cGUuXG4gKi9cbmV4cG9ydCBjbGFzcyBTcGxpY2VCdWZmZXIge1xuICAvKipcbiAgICogQHBhcmFtIHtSZWFkb25seUFycmF5PFQ+IHwgbnVsbCB8IHVuZGVmaW5lZH0gW2luaXRpYWxdXG4gICAqICAgSW5pdGlhbCBpdGVtcyAob3B0aW9uYWwpLlxuICAgKiBAcmV0dXJuc1xuICAgKiAgIFNwbGljZSBidWZmZXIuXG4gICAqL1xuICBjb25zdHJ1Y3Rvcihpbml0aWFsKSB7XG4gICAgLyoqIEB0eXBlIHtBcnJheTxUPn0gKi9cbiAgICB0aGlzLmxlZnQgPSBpbml0aWFsID8gWy4uLmluaXRpYWxdIDogW11cbiAgICAvKiogQHR5cGUge0FycmF5PFQ+fSAqL1xuICAgIHRoaXMucmlnaHQgPSBbXVxuICB9XG5cbiAgLyoqXG4gICAqIEFycmF5IGFjY2VzcztcbiAgICogZG9lcyBub3QgbW92ZSB0aGUgY3Vyc29yLlxuICAgKlxuICAgKiBAcGFyYW0ge251bWJlcn0gaW5kZXhcbiAgICogICBJbmRleC5cbiAgICogQHJldHVybiB7VH1cbiAgICogICBJdGVtLlxuICAgKi9cbiAgZ2V0KGluZGV4KSB7XG4gICAgaWYgKGluZGV4IDwgMCB8fCBpbmRleCA+PSB0aGlzLmxlZnQubGVuZ3RoICsgdGhpcy5yaWdodC5sZW5ndGgpIHtcbiAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKFxuICAgICAgICAnQ2Fubm90IGFjY2VzcyBpbmRleCBgJyArXG4gICAgICAgICAgaW5kZXggK1xuICAgICAgICAgICdgIGluIGEgc3BsaWNlIGJ1ZmZlciBvZiBzaXplIGAnICtcbiAgICAgICAgICAodGhpcy5sZWZ0Lmxlbmd0aCArIHRoaXMucmlnaHQubGVuZ3RoKSArXG4gICAgICAgICAgJ2AnXG4gICAgICApXG4gICAgfVxuXG4gICAgaWYgKGluZGV4IDwgdGhpcy5sZWZ0Lmxlbmd0aCkgcmV0dXJuIHRoaXMubGVmdFtpbmRleF1cbiAgICByZXR1cm4gdGhpcy5yaWdodFt0aGlzLnJpZ2h0Lmxlbmd0aCAtIGluZGV4ICsgdGhpcy5sZWZ0Lmxlbmd0aCAtIDFdXG4gIH1cblxuICAvKipcbiAgICogVGhlIGxlbmd0aCBvZiB0aGUgc3BsaWNlIGJ1ZmZlciwgb25lIGdyZWF0ZXIgdGhhbiB0aGUgbGFyZ2VzdCBpbmRleCBpbiB0aGVcbiAgICogYXJyYXkuXG4gICAqL1xuICBnZXQgbGVuZ3RoKCkge1xuICAgIHJldHVybiB0aGlzLmxlZnQubGVuZ3RoICsgdGhpcy5yaWdodC5sZW5ndGhcbiAgfVxuXG4gIC8qKlxuICAgKiBSZW1vdmUgYW5kIHJldHVybiBgbGlzdFswXWA7XG4gICAqIG1vdmVzIHRoZSBjdXJzb3IgdG8gYDBgLlxuICAgKlxuICAgKiBAcmV0dXJucyB7VCB8IHVuZGVmaW5lZH1cbiAgICogICBJdGVtLCBvcHRpb25hbC5cbiAgICovXG4gIHNoaWZ0KCkge1xuICAgIHRoaXMuc2V0Q3Vyc29yKDApXG4gICAgcmV0dXJuIHRoaXMucmlnaHQucG9wKClcbiAgfVxuXG4gIC8qKlxuICAgKiBTbGljZSB0aGUgYnVmZmVyIHRvIGdldCBhbiBhcnJheTtcbiAgICogZG9lcyBub3QgbW92ZSB0aGUgY3Vyc29yLlxuICAgKlxuICAgKiBAcGFyYW0ge251bWJlcn0gc3RhcnRcbiAgICogICBTdGFydC5cbiAgICogQHBhcmFtIHtudW1iZXIgfCBudWxsIHwgdW5kZWZpbmVkfSBbZW5kXVxuICAgKiAgIEVuZCAob3B0aW9uYWwpLlxuICAgKiBAcmV0dXJucyB7QXJyYXk8VD59XG4gICAqICAgQXJyYXkgb2YgaXRlbXMuXG4gICAqL1xuICBzbGljZShzdGFydCwgZW5kKSB7XG4gICAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gICAgY29uc3Qgc3RvcCA9XG4gICAgICBlbmQgPT09IG51bGwgfHwgZW5kID09PSB1bmRlZmluZWQgPyBOdW1iZXIuUE9TSVRJVkVfSU5GSU5JVFkgOiBlbmRcblxuICAgIGlmIChzdG9wIDwgdGhpcy5sZWZ0Lmxlbmd0aCkge1xuICAgICAgcmV0dXJuIHRoaXMubGVmdC5zbGljZShzdGFydCwgc3RvcClcbiAgICB9XG5cbiAgICBpZiAoc3RhcnQgPiB0aGlzLmxlZnQubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gdGhpcy5yaWdodFxuICAgICAgICAuc2xpY2UoXG4gICAgICAgICAgdGhpcy5yaWdodC5sZW5ndGggLSBzdG9wICsgdGhpcy5sZWZ0Lmxlbmd0aCxcbiAgICAgICAgICB0aGlzLnJpZ2h0Lmxlbmd0aCAtIHN0YXJ0ICsgdGhpcy5sZWZ0Lmxlbmd0aFxuICAgICAgICApXG4gICAgICAgIC5yZXZlcnNlKClcbiAgICB9XG5cbiAgICByZXR1cm4gdGhpcy5sZWZ0XG4gICAgICAuc2xpY2Uoc3RhcnQpXG4gICAgICAuY29uY2F0KFxuICAgICAgICB0aGlzLnJpZ2h0LnNsaWNlKHRoaXMucmlnaHQubGVuZ3RoIC0gc3RvcCArIHRoaXMubGVmdC5sZW5ndGgpLnJldmVyc2UoKVxuICAgICAgKVxuICB9XG5cbiAgLyoqXG4gICAqIE1pbWljcyB0aGUgYmVoYXZpb3Igb2YgQXJyYXkucHJvdG90eXBlLnNwbGljZSgpIGV4Y2VwdCBmb3IgdGhlIGNoYW5nZSBvZlxuICAgKiBpbnRlcmZhY2UgbmVjZXNzYXJ5IHRvIGF2b2lkIHNlZ2ZhdWx0cyB3aGVuIHBhdGNoaW5nIGluIHZlcnkgbGFyZ2UgYXJyYXlzLlxuICAgKlxuICAgKiBUaGlzIG9wZXJhdGlvbiBtb3ZlcyBjdXJzb3IgaXMgbW92ZWQgdG8gYHN0YXJ0YCBhbmQgcmVzdWx0cyBpbiB0aGUgY3Vyc29yXG4gICAqIHBsYWNlZCBhZnRlciBhbnkgaW5zZXJ0ZWQgaXRlbXMuXG4gICAqXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBzdGFydFxuICAgKiAgIFN0YXJ0O1xuICAgKiAgIHplcm8tYmFzZWQgaW5kZXggYXQgd2hpY2ggdG8gc3RhcnQgY2hhbmdpbmcgdGhlIGFycmF5O1xuICAgKiAgIG5lZ2F0aXZlIG51bWJlcnMgY291bnQgYmFja3dhcmRzIGZyb20gdGhlIGVuZCBvZiB0aGUgYXJyYXkgYW5kIHZhbHVlc1xuICAgKiAgIHRoYXQgYXJlIG91dC1vZiBib3VuZHMgYXJlIGNsYW1wZWQgdG8gdGhlIGFwcHJvcHJpYXRlIGVuZCBvZiB0aGUgYXJyYXkuXG4gICAqIEBwYXJhbSB7bnVtYmVyIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2RlbGV0ZUNvdW50PTBdXG4gICAqICAgRGVsZXRlIGNvdW50IChkZWZhdWx0OiBgMGApO1xuICAgKiAgIG1heGltdW0gbnVtYmVyIG9mIGVsZW1lbnRzIHRvIGRlbGV0ZSwgc3RhcnRpbmcgZnJvbSBzdGFydC5cbiAgICogQHBhcmFtIHtBcnJheTxUPiB8IG51bGwgfCB1bmRlZmluZWR9IFtpdGVtcz1bXV1cbiAgICogICBJdGVtcyB0byBpbmNsdWRlIGluIHBsYWNlIG9mIHRoZSBkZWxldGVkIGl0ZW1zIChkZWZhdWx0OiBgW11gKS5cbiAgICogQHJldHVybiB7QXJyYXk8VD59XG4gICAqICAgQW55IHJlbW92ZWQgaXRlbXMuXG4gICAqL1xuICBzcGxpY2Uoc3RhcnQsIGRlbGV0ZUNvdW50LCBpdGVtcykge1xuICAgIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICAgIGNvbnN0IGNvdW50ID0gZGVsZXRlQ291bnQgfHwgMFxuXG4gICAgdGhpcy5zZXRDdXJzb3IoTWF0aC50cnVuYyhzdGFydCkpXG4gICAgY29uc3QgcmVtb3ZlZCA9IHRoaXMucmlnaHQuc3BsaWNlKFxuICAgICAgdGhpcy5yaWdodC5sZW5ndGggLSBjb3VudCxcbiAgICAgIE51bWJlci5QT1NJVElWRV9JTkZJTklUWVxuICAgIClcbiAgICBpZiAoaXRlbXMpIGNodW5rZWRQdXNoKHRoaXMubGVmdCwgaXRlbXMpXG4gICAgcmV0dXJuIHJlbW92ZWQucmV2ZXJzZSgpXG4gIH1cblxuICAvKipcbiAgICogUmVtb3ZlIGFuZCByZXR1cm4gdGhlIGhpZ2hlc3QtbnVtYmVyZWQgaXRlbSBpbiB0aGUgYXJyYXksIHNvXG4gICAqIGBsaXN0W2xpc3QubGVuZ3RoIC0gMV1gO1xuICAgKiBNb3ZlcyB0aGUgY3Vyc29yIHRvIGBsZW5ndGhgLlxuICAgKlxuICAgKiBAcmV0dXJucyB7VCB8IHVuZGVmaW5lZH1cbiAgICogICBJdGVtLCBvcHRpb25hbC5cbiAgICovXG4gIHBvcCgpIHtcbiAgICB0aGlzLnNldEN1cnNvcihOdW1iZXIuUE9TSVRJVkVfSU5GSU5JVFkpXG4gICAgcmV0dXJuIHRoaXMubGVmdC5wb3AoKVxuICB9XG5cbiAgLyoqXG4gICAqIEluc2VydHMgYSBzaW5nbGUgaXRlbSB0byB0aGUgaGlnaC1udW1iZXJlZCBzaWRlIG9mIHRoZSBhcnJheTtcbiAgICogbW92ZXMgdGhlIGN1cnNvciB0byBgbGVuZ3RoYC5cbiAgICpcbiAgICogQHBhcmFtIHtUfSBpdGVtXG4gICAqICAgSXRlbS5cbiAgICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAgICogICBOb3RoaW5nLlxuICAgKi9cbiAgcHVzaChpdGVtKSB7XG4gICAgdGhpcy5zZXRDdXJzb3IoTnVtYmVyLlBPU0lUSVZFX0lORklOSVRZKVxuICAgIHRoaXMubGVmdC5wdXNoKGl0ZW0pXG4gIH1cblxuICAvKipcbiAgICogSW5zZXJ0cyBtYW55IGl0ZW1zIHRvIHRoZSBoaWdoLW51bWJlcmVkIHNpZGUgb2YgdGhlIGFycmF5LlxuICAgKiBNb3ZlcyB0aGUgY3Vyc29yIHRvIGBsZW5ndGhgLlxuICAgKlxuICAgKiBAcGFyYW0ge0FycmF5PFQ+fSBpdGVtc1xuICAgKiAgIEl0ZW1zLlxuICAgKiBAcmV0dXJucyB7dW5kZWZpbmVkfVxuICAgKiAgIE5vdGhpbmcuXG4gICAqL1xuICBwdXNoTWFueShpdGVtcykge1xuICAgIHRoaXMuc2V0Q3Vyc29yKE51bWJlci5QT1NJVElWRV9JTkZJTklUWSlcbiAgICBjaHVua2VkUHVzaCh0aGlzLmxlZnQsIGl0ZW1zKVxuICB9XG5cbiAgLyoqXG4gICAqIEluc2VydHMgYSBzaW5nbGUgaXRlbSB0byB0aGUgbG93LW51bWJlcmVkIHNpZGUgb2YgdGhlIGFycmF5O1xuICAgKiBNb3ZlcyB0aGUgY3Vyc29yIHRvIGAwYC5cbiAgICpcbiAgICogQHBhcmFtIHtUfSBpdGVtXG4gICAqICAgSXRlbS5cbiAgICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAgICogICBOb3RoaW5nLlxuICAgKi9cbiAgdW5zaGlmdChpdGVtKSB7XG4gICAgdGhpcy5zZXRDdXJzb3IoMClcbiAgICB0aGlzLnJpZ2h0LnB1c2goaXRlbSlcbiAgfVxuXG4gIC8qKlxuICAgKiBJbnNlcnRzIG1hbnkgaXRlbXMgdG8gdGhlIGxvdy1udW1iZXJlZCBzaWRlIG9mIHRoZSBhcnJheTtcbiAgICogbW92ZXMgdGhlIGN1cnNvciB0byBgMGAuXG4gICAqXG4gICAqIEBwYXJhbSB7QXJyYXk8VD59IGl0ZW1zXG4gICAqICAgSXRlbXMuXG4gICAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gICAqICAgTm90aGluZy5cbiAgICovXG4gIHVuc2hpZnRNYW55KGl0ZW1zKSB7XG4gICAgdGhpcy5zZXRDdXJzb3IoMClcbiAgICBjaHVua2VkUHVzaCh0aGlzLnJpZ2h0LCBpdGVtcy5yZXZlcnNlKCkpXG4gIH1cblxuICAvKipcbiAgICogTW92ZSB0aGUgY3Vyc29yIHRvIGEgc3BlY2lmaWMgcG9zaXRpb24gaW4gdGhlIGFycmF5LiBSZXF1aXJlc1xuICAgKiB0aW1lIHByb3BvcnRpb25hbCB0byB0aGUgZGlzdGFuY2UgbW92ZWQuXG4gICAqXG4gICAqIElmIGBuIDwgMGAsIHRoZSBjdXJzb3Igd2lsbCBlbmQgdXAgYXQgdGhlIGJlZ2lubmluZy5cbiAgICogSWYgYG4gPiBsZW5ndGhgLCB0aGUgY3Vyc29yIHdpbGwgZW5kIHVwIGF0IHRoZSBlbmQuXG4gICAqXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBuXG4gICAqICAgUG9zaXRpb24uXG4gICAqIEByZXR1cm4ge3VuZGVmaW5lZH1cbiAgICogICBOb3RoaW5nLlxuICAgKi9cbiAgc2V0Q3Vyc29yKG4pIHtcbiAgICBpZiAoXG4gICAgICBuID09PSB0aGlzLmxlZnQubGVuZ3RoIHx8XG4gICAgICAobiA+IHRoaXMubGVmdC5sZW5ndGggJiYgdGhpcy5yaWdodC5sZW5ndGggPT09IDApIHx8XG4gICAgICAobiA8IDAgJiYgdGhpcy5sZWZ0Lmxlbmd0aCA9PT0gMClcbiAgICApXG4gICAgICByZXR1cm5cbiAgICBpZiAobiA8IHRoaXMubGVmdC5sZW5ndGgpIHtcbiAgICAgIC8vIE1vdmUgY3Vyc29yIHRvIHRoZSB0aGlzLmxlZnRcbiAgICAgIGNvbnN0IHJlbW92ZWQgPSB0aGlzLmxlZnQuc3BsaWNlKG4sIE51bWJlci5QT1NJVElWRV9JTkZJTklUWSlcbiAgICAgIGNodW5rZWRQdXNoKHRoaXMucmlnaHQsIHJlbW92ZWQucmV2ZXJzZSgpKVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBNb3ZlIGN1cnNvciB0byB0aGUgdGhpcy5yaWdodFxuICAgICAgY29uc3QgcmVtb3ZlZCA9IHRoaXMucmlnaHQuc3BsaWNlKFxuICAgICAgICB0aGlzLmxlZnQubGVuZ3RoICsgdGhpcy5yaWdodC5sZW5ndGggLSBuLFxuICAgICAgICBOdW1iZXIuUE9TSVRJVkVfSU5GSU5JVFlcbiAgICAgIClcbiAgICAgIGNodW5rZWRQdXNoKHRoaXMubGVmdCwgcmVtb3ZlZC5yZXZlcnNlKCkpXG4gICAgfVxuICB9XG59XG5cbi8qKlxuICogQXZvaWQgc3RhY2sgb3ZlcmZsb3cgYnkgcHVzaGluZyBpdGVtcyBvbnRvIHRoZSBzdGFjayBpbiBzZWdtZW50c1xuICpcbiAqIEB0ZW1wbGF0ZSBUXG4gKiAgIEl0ZW0gdHlwZS5cbiAqIEBwYXJhbSB7QXJyYXk8VD59IGxpc3RcbiAqICAgTGlzdCB0byBpbmplY3QgaW50by5cbiAqIEBwYXJhbSB7UmVhZG9ubHlBcnJheTxUPn0gcmlnaHRcbiAqICAgSXRlbXMgdG8gaW5qZWN0LlxuICogQHJldHVybiB7dW5kZWZpbmVkfVxuICogICBOb3RoaW5nLlxuICovXG5mdW5jdGlvbiBjaHVua2VkUHVzaChsaXN0LCByaWdodCkge1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgbGV0IGNodW5rU3RhcnQgPSAwXG5cbiAgaWYgKHJpZ2h0Lmxlbmd0aCA8IGNvbnN0YW50cy52OE1heFNhZmVDaHVua1NpemUpIHtcbiAgICBsaXN0LnB1c2goLi4ucmlnaHQpXG4gIH0gZWxzZSB7XG4gICAgd2hpbGUgKGNodW5rU3RhcnQgPCByaWdodC5sZW5ndGgpIHtcbiAgICAgIGxpc3QucHVzaChcbiAgICAgICAgLi4ucmlnaHQuc2xpY2UoY2h1bmtTdGFydCwgY2h1bmtTdGFydCArIGNvbnN0YW50cy52OE1heFNhZmVDaHVua1NpemUpXG4gICAgICApXG4gICAgICBjaHVua1N0YXJ0ICs9IGNvbnN0YW50cy52OE1heFNhZmVDaHVua1NpemVcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJjb25zdGFudHMiLCJTcGxpY2VCdWZmZXIiLCJjb25zdHJ1Y3RvciIsImluaXRpYWwiLCJsZWZ0IiwicmlnaHQiLCJnZXQiLCJpbmRleCIsImxlbmd0aCIsIlJhbmdlRXJyb3IiLCJzaGlmdCIsInNldEN1cnNvciIsInBvcCIsInNsaWNlIiwic3RhcnQiLCJlbmQiLCJzdG9wIiwidW5kZWZpbmVkIiwiTnVtYmVyIiwiUE9TSVRJVkVfSU5GSU5JVFkiLCJyZXZlcnNlIiwiY29uY2F0Iiwic3BsaWNlIiwiZGVsZXRlQ291bnQiLCJpdGVtcyIsImNvdW50IiwiTWF0aCIsInRydW5jIiwicmVtb3ZlZCIsImNodW5rZWRQdXNoIiwicHVzaCIsIml0ZW0iLCJwdXNoTWFueSIsInVuc2hpZnQiLCJ1bnNoaWZ0TWFueSIsIm4iLCJsaXN0IiwiY2h1bmtTdGFydCIsInY4TWF4U2FmZUNodW5rU2l6ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\n");

/***/ })

};
;